<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="GigGenius - Professional freelancer profile and portfolio">
    <title>GigGenius - Profile</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --text-light: #ffffff;
            --text-dark: #333333;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-400: #ced4da;
            --gray-500: #adb5bd;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --border-radius-sm: 4px;
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 20px;
            --transition-fast: 0.2s ease;
            --transition: 0.3s ease;
        }

        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--text-dark);
            line-height: 1.6;
            min-height: 100vh;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        ul {
            list-style: none;
        }

        button, input, select, textarea {
            font-family: inherit;
            font-size: inherit;
            outline: none;
        }

        button {
            cursor: pointer;
            border: none;
            background: none;
        }

        img, video {
            max-width: 100%;
            height: auto;
        }

        /* Layout */
        .header-container {
            max-width: 2000px;
            margin: 0 auto;
            background-color: white;
            box-shadow: var(--shadow-sm);
        }

        .body-container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border-radius: 20px 20px 0 0;
            min-height: calc(100vh - 5rem);
            display: flex;
            flex-direction: column;
            margin-top: 2rem;
            overflow: hidden;
        }

        .main-content {
            padding: 3rem 2rem 4rem;
            flex: 1;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 1rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5rem;
            position: relative;
            z-index: 1000;
            min-height: 5rem;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
            padding-left: 0;
            flex: 1;
        }

        .logo {
            display: flex;
            align-items: center;
            color: var(--primary-pink);
        }

        .logo img {
            width: 3.5rem;
            height: 3.5rem;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-left: 0.5rem;
            margin-right: 0.5rem;
            color: var(--primary-pink);
        }

        .logo:hover, .logo:active {
            color: var(--primary-blue);
        }

        .logo:hover h1 {
            color: var(--primary-blue);
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
            height: 100%;
            margin: 0;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1rem;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.3s ease;
            position: relative;
        }
        /* Remove underline from individual nav items */
        .nav-links > a:after,
        .nav-dropbtn:after {
            display: none !important;
        }
        .nav-links > a,
        .nav-links > a:hover,
        .nav-links > a.active,
        .nav-dropbtn,
        .nav-dropbtn:hover,
        .nav-dropbtn.active,
        .nav-dropdown-content a,
        .nav-dropdown-content a:hover {
            text-decoration: none !important;
            border-bottom: none !important;
        }
        .nav-links > a:hover, .nav-links > a.active,
        .nav-dropbtn:hover, .nav-dropbtn.active {
            color: var(--primary-pink);
        }
        .nav-dropdown-content a:after {
            display: none !important;
        }
        .navbar {
            position: relative;
        }
        .nav-links > a:hover, .nav-links > a.active {
            color: var(--primary-pink);
            background-color: transparent;
        }
        .nav-dropbtn:hover, .nav-dropbtn.active {
            color: var(--primary-pink);
            background-color: transparent;
        }

        .nav-dropbtn {
            font-weight: 600;
            font-size: 1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .nav-dropbtn {
            font-weight: 600;
            font-size: 1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
            position: relative;
        }

        .nav-dropbtn:hover, .nav-dropbtn.active {
            color: var(--primary-pink);
            background-color: transparent;
            border-bottom: 2px solid var(--primary-pink);
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            top: 100%;
            left: 0;
        }

        .nav-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 1rem;
        }

        .nav-dropdown-content a:hover {
            background-color: transparent;
            color: var(--primary-pink);
        }

        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }

        /* Right section container */
        .right-section {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            padding-right: 1rem;
            justify-content: flex-end;
        }

        /* Search and Auth Styles */
        .search-container {
            display: flex;
            align-items: center;
            margin-right: 1rem;
        }

        .search-bar {
            position: relative;
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 25px;
            padding: 0.4rem 1rem;
            width: 200px;
            transition: all 0.3s ease;
        }

        .search-bar:focus-within {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px rgba(0, 74, 173, 0.1);
        }

        .search-bar input {
            border: none;
            background: transparent;
            outline: none;
            flex: 1;
            font-size: 0.9rem;
            color: #333;
        }

        .search-bar input::placeholder {
            color: #6c757d;
        }

        .search-bar .icon {
            color: var(--primary-blue);
            cursor: pointer;
            margin-left: 0.5rem;
        }

        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Notification Styles */
        .notification-container {
            position: relative;
        }

        .notification-icon {
            position: relative;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background-color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: transparent;
        }

        .notification-icon:hover {
            background-color: transparent;
        }

        .notification-icon i {
            font-size: 1.2rem;
            color: var(--primary-blue);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: var(--primary-pink);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;
        }

        .notification-dropdown {
            position: absolute;
            top: calc(100% + 10px);
            right: 0;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            width: 380px;
            max-height: 500px;
            overflow: hidden;
            z-index: 1000;
            border: 1px solid rgba(0, 74, 173, 0.08);
            display: none;
        }

        .notification-dropdown.active {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .notification-header {
            padding: 16px 20px 12px;
            border-bottom: 1px solid rgba(0, 74, 173, 0.06);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 700;
            color: var(--primary-blue);
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            position: relative;
        }

        .notification-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            border-radius: 16px 16px 0 0;
        }

        .notification-header-actions {
            font-size: 0.8rem;
            color: var(--primary-pink);
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 8px;
            transition: all 0.2s ease;
            font-weight: 600;
            background: rgba(205, 32, 139, 0.05);
            border: 1px solid rgba(205, 32, 139, 0.1);
        }

        .notification-header-actions:hover {
            background: rgba(205, 32, 139, 0.1);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(205, 32, 139, 0.15);
        }

        .empty-notifications {
            padding: 40px 20px;
            text-align: center;
            color: #6b7280;
            background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
        }

        .empty-notifications i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.3;
        }

        .empty-notifications h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .empty-notifications p {
            margin: 0;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Profile Dropdown Styles */
        .profile-dropdown {
            position: relative;
        }

        .profile-button {
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid var(--primary-blue);
            transition: all 0.3s ease;
        }

        .profile-button:hover {
            border-color: var(--primary-pink);
            transform: scale(1.05);
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-dropdown-content {
            position: absolute;
            top: calc(100% + 10px);
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            min-width: 200px;
            z-index: 1000;
            border: 1px solid rgba(0, 74, 173, 0.08);
            overflow: hidden;
            display: none;
        }

        .profile-dropdown-content.active {
            display: block;
            animation: slideDown 0.3s ease;
        }

        .profile-dropdown-content a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.2s ease;
            border-bottom: 1px solid rgba(0, 74, 173, 0.05);
        }

        .profile-dropdown-content a:last-child {
            border-bottom: none;
        }

        .profile-dropdown-content a:hover {
            background: rgba(0, 74, 173, 0.05);
            color: var(--primary-pink);
            transform: translateX(4px);
        }

        .profile-dropdown-content a i {
            width: 16px;
            text-align: center;
            opacity: 0.7;
        }

        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 8px 0;
        }

        .logout-option {
            color: #dc3545 !important;
        }

        .logout-option:hover {
            background-color: #fff5f5 !important;
            color: #dc3545 !important;
        }

        /* Show dropdown on click */
        .profile-dropdown.active .profile-dropdown-content {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Mobile Responsive Styles */
        @media (max-width: 992px) {
            .navbar-left {
                padding-left: 0;
            }

            .logo img {
                width: 2.4rem;
                height: 2.4rem;
                border-radius: 50%;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                flex-shrink: 0;
            }

            .logo h1 {
                font-size: 1.1rem;
                margin: 0;
                white-space: nowrap;
                font-weight: 600;
                color: var(--primary-pink);
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .nav-links {
                gap: 0.5rem;
            }

            .nav-links a {
                font-size: 0.9rem;
                padding: 0.4rem 0.8rem;
            }

            .nav-dropbtn {
                font-size: 0.9rem;
                padding: 0.4rem 0.8rem;
            }

            .search-bar {
                width: 160px;
                height: 36px;
            }
        }

        @media (max-width: 768px) {
            .navbar {
                height: 4rem;
                padding: 0 0.8rem;
            }

            .logo h1 {
                font-size: 1.2rem;
            }

            .nav-links {
                gap: 0.3rem;
            }

            .nav-links a {
                font-size: 0.8rem;
                padding: 0.3rem 0.6rem;
            }

            .nav-dropbtn {
                font-size: 0.8rem;
                padding: 0.3rem 0.6rem;
            }

            .search-bar {
                width: 140px;
                height: 34px;
            }

            .right-section {
                gap: 0.8rem;
            }

            .body-container {
                margin-top: 1rem;
                border-radius: 16px 16px 0 0;
            }

            .main-content {
                padding: 2rem 1rem 3rem;
            }

            .profile-name {
                font-size: 2rem;
            }

            .profile-title {
                font-size: 1.25rem;
            }

            .profile-stats {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stat-item {
                padding: 1rem;
            }

            .section-header {
                padding: 1.5rem 1.5rem 1rem;
            }

            .section-content {
                padding: 1.5rem;
            }

            .section-title {
                font-size: 1.25rem;
            }
        }

        /* Scroll animations */
        @keyframes slideInFromLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInFromRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .section-card:nth-child(odd) {
            animation: slideInFromLeft 0.8s ease-out;
        }

        .section-card:nth-child(even) {
            animation: slideInFromRight 0.8s ease-out;
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Enhanced focus states */
        *:focus {
            outline: 2px solid var(--primary-blue);
            outline-offset: 2px;
        }

        /* Loading animation for images */
        .portfolio-image {
            position: relative;
            overflow: hidden;
        }

        .portfolio-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }



        /* Profile Section */
        .profile-section {
            margin-bottom: 3rem;
            position: relative;
        }

        .profile-section::before {
            content: '';
            position: absolute;
            top: -1rem;
            left: -2rem;
            right: -2rem;
            height: 200px;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            opacity: 0.05;
            border-radius: 20px;
            z-index: -1;
        }

        .profile-header {
            margin-bottom: 2rem;
            text-align: center;
            padding: 2rem 0;
        }

        .profile-name {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            letter-spacing: -0.02em;
            animation: fadeInUp 0.8s ease-out;
        }

        .profile-title {
            font-size: 1.5rem;
            color: var(--gray-600);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            font-weight: 500;
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        .profile-title i {
            color: var(--primary-pink);
            font-size: 1.25rem;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .profile-row {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        @media (min-width: 1024px) {
            .profile-row {
                flex-direction: row;
            }
        }

        .video-card, .profile-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .video-card::before, .profile-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .video-card:hover, .profile-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
        }

        .video-card:hover::before, .profile-card:hover::before {
            opacity: 1;
        }

        @media (min-width: 1024px) {
            .video-card, .profile-card {
                width: 50%;
            }
        }

        /* Video Container - Centered */
        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
            overflow: hidden;
            background-color: #000;
        }

        .video-container video {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .expand-btn {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            z-index: 2;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.875rem;
        }

        .expand-btn:hover {
            background: rgba(0, 0, 0, 0.7);
        }

        .video-container.expanded {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            padding: 0;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .video-container.expanded video {
            width: 90%;
            height: 90%;
            object-fit: contain;
        }

        /* Profile Content */
        .profile-content {
            padding: 2.5rem;
            position: relative;
        }

        .profile-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            margin-bottom: 2.5rem;
        }

        @media (min-width: 768px) {
            .profile-stats {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        .stat-item {
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
        }

        .stat-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            display: block;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .profile-summary {
            margin-bottom: 1.5rem;
        }

        .profile-summary h3 {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .profile-summary h3 i {
            color: var(--primary-blue);
        }

        .summary-text {
            color: var(--gray-600);
            margin-bottom: 0.5rem;
        }

        .show-more {
            color: var(--primary-pink);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
        }

        .show-more:hover {
            text-decoration: underline;
        }

        .edit-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            color: var(--primary-pink);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            margin-left: 0.5rem;
            background: none;
            border: none;
        }

        .edit-btn:hover {
            text-decoration: underline;
        }

        .profile-fields {
            margin-bottom: 1.5rem;
        }

        .field-group {
            margin-bottom: 1rem;
        }

        .field-group label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.375rem;
            color: var(--gray-700);
        }

        .field-input {
            width: 100%;
            padding: 0.625rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            background-color: #f9fafb;
            color: #333;
        }

        .edit-profile-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 2rem;
            background: linear-gradient(135deg, var(--primary-pink) 0%, #e91e63 100%);
            color: white;
            border: none;
            border-radius: 16px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            width: 100%;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(212, 27, 140, 0.3);
            position: relative;
            overflow: hidden;
        }

        .edit-profile-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .edit-profile-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(212, 27, 140, 0.4);
        }

        .edit-profile-btn:hover::before {
            left: 100%;
        }

        /* Portfolio and Work History Section */
        .portfolio-section {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .section-row {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        @media (min-width: 1024px) {
            .section-row {
                flex-direction: row;
            }
        }

        .section-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 24px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
            width: 100%;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .section-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .section-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
        }

        .section-card:hover::before {
            opacity: 1;
        }

        @media (min-width: 1024px) {
            .section-card {
                width: 50%;
            }
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2rem 2.5rem 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.6) 100%);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: var(--text-dark);
            letter-spacing: -0.01em;
        }

        .section-title i {
            color: var(--primary-blue);
            font-size: 1.25rem;
            padding: 0.5rem;
            background: rgba(0, 74, 173, 0.1);
            border-radius: 12px;
        }

        .section-content {
            padding: 2.5rem;
            background: rgba(255, 255, 255, 0.5);
        }

        .introduction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .textarea {
            width: 100%;
            min-height: 150px;
            padding: 0.75rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            resize: vertical;
            margin-bottom: 1.25rem;
            background-color: #f9fafb;
        }

        .section-actions {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .arrow-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background-color: #f3f4f6;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s;
        }

        .arrow-btn:hover {
            background-color: #e5e7eb;
            color: #333;
        }

        .add-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, var(--primary-pink) 0%, #e91e63 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 6px 20px rgba(212, 27, 140, 0.25);
            position: relative;
            overflow: hidden;
        }

        .add-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .add-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(212, 27, 140, 0.35);
        }

        .add-btn:hover::before {
            left: 100%;
        }

        .portfolio-image {
            width: 100%;
            height: 250px;
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 1.25rem;
            background-color: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
        }

        .portfolio-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .portfolio-image:hover img {
            transform: scale(1.05);
        }

        /* New Portfolio Design */
        .portfolio-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding: 0 1.25rem;
        }

        .portfolio-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }

        .portfolio-actions {
            display: flex;
            gap: 0.5rem;
        }

        .portfolio-add-btn, .portfolio-refresh-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid var(--primary-blue);
            background: white;
            color: var(--primary-blue);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .portfolio-add-btn:hover, .portfolio-refresh-btn:hover {
            background: var(--primary-blue);
            color: white;
        }

        .portfolio-tabs {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
            padding: 0 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .portfolio-tab {
            background: none;
            border: none;
            padding: 0.75rem 0;
            font-size: 1rem;
            font-weight: 500;
            color: var(--gray-600);
            cursor: pointer;
            position: relative;
            transition: color 0.2s;
        }

        .portfolio-tab.active {
            color: var(--text-dark);
        }

        .portfolio-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--text-dark);
        }

        .portfolio-tab:hover {
            color: var(--text-dark);
        }

        .portfolio-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            padding: 0 1.25rem;
            margin-bottom: 2rem;
        }

        .portfolio-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }

        .portfolio-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .portfolio-card-image {
            width: 100%;
            height: 200px;
            overflow: hidden;
        }

        .portfolio-card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .portfolio-card:hover .portfolio-card-image img {
            transform: scale(1.05);
        }

        .portfolio-card-title {
            padding: 1rem;
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: var(--primary-green);
            line-height: 1.4;
        }

        .portfolio-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            padding: 0 1.25rem;
        }

        .pagination-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 1px solid #e5e7eb;
            background: white;
            color: var(--gray-600);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 500;
        }

        .pagination-btn:hover {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        .pagination-btn.active {
            background: var(--text-dark);
            color: white;
            border-color: var(--text-dark);
        }

        .pagination-dots {
            color: var(--gray-600);
            margin: 0 0.5rem;
        }

        .input-field {
            width: 100%;
            padding: 0.625rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .action-edit-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-blue);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .action-edit-btn:hover {
            background-color: #1d4ed8;
        }

        .delete-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-pink);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .delete-btn:hover {
            background-color: #b91c77;
        }

        .work-history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.25rem;
        }

        .work-history-title {
            display: inline-block;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-card {
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            margin-bottom: 1.25rem;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .certification-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .certification-header {
            padding: 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .certification-title {
            font-size: 1.125rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-title i {
            color: var(--primary-pink);
        }

        .certification-content {
            padding: 1.25rem;
        }

        .certification-detail {
            margin-bottom: 0.75rem;
        }

        .certification-detail strong {
            font-weight: 600;
        }

        .certification-description {
            margin-top: 1rem;
        }

        .certification-description h4 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-description h4 i {
            color: var(--primary-blue);
        }

        .certification-description p {
            color: var(--gray-600);
        }

        /* Footer */
        footer {
            background: var(--primary-blue);
            padding: 2rem 5%;
            align-items: center;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            gap: 2rem;
            margin-bottom: 2rem;
        }

        @media (min-width: 768px) {
            .footer-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1024px) {
            .footer-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        .footer-column h3 {
            margin-bottom: 1rem;
            color: var(--text-light);
            font-weight: 600;
        }

        .footer-column a {
            display: block;
            color: var(--text-light);
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease;
            opacity: 0.8;
        }

        .footer-column a:hover {
            text-decoration: underline;
            opacity: 1;
        }

        .footer-bottom {
            color: var(--text-light);
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        @media (max-width: 768px) {
            .footer-bottom {
                flex-direction: column;
                text-align: center;
            }
        }

        .footer-bottom p {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 400;
        }

        .social-icons {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .social-icons a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            margin: 0;
        }

        .social-icons .bi {
            font-size: 1.2rem;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons a:hover {
            background-color: var(--primary-pink);
            transform: translateY(-3px);
        }

        .footer-bottom a {
            color: var(--text-light);
            margin: 0 10px;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            text-decoration: underline;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background-color: white;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            overflow-y: auto;
        }

        .modal-header {
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-header-content {
            display: flex;
            align-items: center;
            height: 80px;
            padding: 0 2rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000;
            margin-bottom: 20px;
            text-align: center;
        }

        .modal-textarea {
            width: 100%;
            height: 300px;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 20px;
            outline: none;
            resize: none;
            font-size: 16px;
            font-family: inherit;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .modal-textarea:focus {
            border-color: #8b5cf6;
            box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
        }

        .modal-buttons {
            margin-top: 20px;
            text-align: center;
        }

        .modal-back-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            background: transparent;
            color: #000;
            text-decoration: underline;
        }

        .modal-back-btn:hover {
            color: #8b5cf6;
        }

        .modal-next-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            background-color: var(--primary-pink);
            color: #fff;
            margin-left: 10px;
        }

        .modal-next-btn:hover {
            background-color: #b91c77;
        }

        /* Portfolio Modal Theme Colors */
        #portfolioAddModal .modal-next-btn {
            background-color: #CD208B;
        }

        #portfolioAddModal .modal-next-btn:hover {
            background-color: #b91c77;
        }

        #portfolioAddModal h2 {
            color: #004AAD;
        }

        /* Portfolio Modal Content Buttons */
        #portfolioAddModal button[style*="border: 2px solid"] {
            border-color: #004AAD !important;
            color: #004AAD !important;
        }

        #portfolioAddModal button[style*="border: 2px solid"]:hover {
            background-color: #004AAD !important;
            color: white !important;
        }

        /* Portfolio Content Styling */
        .portfolio-card .content-block img {
            max-width: 100% !important;
            height: auto !important;
            border-radius: 4px;
            margin: 0.5rem 0;
        }

        .portfolio-card .content-block video {
            max-width: 100% !important;
            height: auto !important;
            border-radius: 4px;
            margin: 0.5rem 0;
        }

        .portfolio-card .content-block {
            margin-bottom: 0.5rem !important;
            padding: 0.5rem !important;
            border: 1px solid #eee !important;
            border-radius: 4px !important;
        }

        /* Hide any unwanted text content in project content area */
        .project-content p:empty,
        .project-content div:empty,
        .project-content span:empty {
            display: none !important;
        }

        /* Hide text nodes that might contain file paths or HTML code */
        .project-content {
            overflow: hidden;
        }

        /* Hide all text content in project content area - show only images and videos */
        .project-content p,
        .project-content div:not(.content-block),
        .project-content span,
        .project-content h1,
        .project-content h2,
        .project-content h3,
        .project-content h4,
        .project-content h5,
        .project-content h6,
        .project-content b,
        .project-content strong,
        .project-content em,
        .project-content i:not(.fas):not(.far):not(.fab),
        .project-content small,
        .project-content code,
        .project-content pre {
            display: none !important;
        }

        /* Hide all text nodes and HTML entities */
        .project-content {
            font-size: 0 !important;
            line-height: 0 !important;
        }

        /* Show only image and video content blocks */
        .project-content .content-block {
            display: block !important;
            font-size: 0 !important;
        }

        /* Hide text blocks, PDF blocks, and link blocks */
        .project-content .text-block,
        .project-content .pdf-block,
        .project-content .link-block {
            display: none !important;
        }

        /* Hide any text within content blocks except for images and videos */
        .project-content .content-block p,
        .project-content .content-block span,
        .project-content .content-block h1,
        .project-content .content-block h2,
        .project-content .content-block h3,
        .project-content .content-block h4,
        .project-content .content-block h5,
        .project-content .content-block h6,
        .project-content .content-block b,
        .project-content .content-block strong,
        .project-content .content-block em,
        .project-content .content-block i:not(.fas):not(.far):not(.fab),
        .project-content .content-block small,
        .project-content .content-block code,
        .project-content .content-block pre,
        .project-content .content-block div:not(:has(img)):not(:has(video)):not(:has(audio)) {
            display: none !important;
        }

        /* Ensure images and videos are always visible */
        .project-content .content-block img,
        .project-content .content-block video,
        .project-content .content-block audio {
            display: block !important;
            font-size: initial !important;
        }

        /* Hide any stray text nodes that might contain HTML entities */
        .project-content::before,
        .project-content::after {
            content: none !important;
        }

        /* Remove any text content from content blocks */
        .project-content .content-block {
            color: transparent !important;
            text-indent: -9999px !important;
        }

        /* But keep images visible */
        .project-content .content-block img,
        .project-content .content-block video,
        .project-content .content-block audio {
            color: initial !important;
            text-indent: 0 !important;
        }

        .project-content > *:not(img):not(video):not(audio):not(.content-block) {
            display: none !important;
        }

        /* Edit Profile Modal */
        .profile-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        @media (min-width: 1024px) {
            .profile-form {
                flex-direction: row;
                align-items: flex-start;
            }
        }

        .profile-photo-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
        }

        .profile-photo {
            width: 150px;
            height: 150px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            border-radius: 8px;
        }

        .profile-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .upload-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: #f3f4f6;
            color: #333;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .upload-btn:hover {
            background-color: #e5e7eb;
        }

        .upload-note {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .profile-details-section {
            flex: 1;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-group label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--primary-blue);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-control {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid rgba(0, 74, 173, 0.1);
            border-radius: 12px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            color: #333;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 4px rgba(0, 74, 173, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.95);
        }

        .form-control[readonly] {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            cursor: not-allowed;
            border-color: rgba(0, 0, 0, 0.1);
        }

        .form-row {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        @media (min-width: 640px) {
            .form-row {
                flex-direction: row;
            }
        }

        .form-col {
            flex: 1;
        }

        .next-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background-color: var(--primary-pink);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            width: 100%;
            margin-top: 1rem;
        }

        .next-btn:hover {
            background-color: #b91c77;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                padding: 0 1rem;
            }

            .main-content {
                padding: 1rem;
            }

            .profile-name {
                font-size: 1.5rem;
            }

            .profile-title {
                font-size: 1rem;
            }

            .modal-content {
                width: 95%;
                margin: 1rem;
            }

            .modal-body {
                padding: 1rem;
            }

            .profile-form {
                flex-direction: column;
            }
        }

        /* Utility Classes */
        .hidden {
            display: none;
        }

        .flex {
            display: flex;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        .gap-4 {
            gap: 1rem;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .mb-6 {
            margin-bottom: 1.5rem;
        }

        .text-center {
            text-align: center;
        }

        .font-bold {
            font-weight: bold;
        }

        .font-semibold {
            font-weight: 600;
        }

        .text-sm {
            font-size: 0.875rem;
        }

        .text-lg {
            font-size: 1.125rem;
        }

        .text-xl {
            font-size: 1.25rem;
        }

        .text-2xl {
            font-size: 1.5rem;
        }

        .text-3xl {
            font-size: 1.875rem;
        }

        .w-full {
            width: 100%;
        }

        .h-full {
            height: 100%;
        }

        .rounded {
            border-radius: 0.25rem;
        }

        .rounded-lg {
            border-radius: 0.5rem;
        }

        .shadow-sm {
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .shadow-md {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .transition-all {
            transition: all 0.3s ease;
        }

        .hover\:shadow-md:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .hover\:transform:hover {
            transform: translateY(-2px);
        }

        /* Star rating */
        .star-rating {
            color: #FBBF24;
        }

        /* Toggle switch */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #22C55E;
            transition: .4s;
            border-radius: 34px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
            transform: translateX(20px);
        }

        /* Work History Styles */
        .work-history-summary {
            background-color: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }

        .work-history-summary h3 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .work-history-summary p {
            color: #374151;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        .work-history-summary .show-more-btn {
            color: var(--primary-blue);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
        }

        .work-history-summary .ai-note {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.75rem;
        }

        .skills-section {
            margin-bottom: 1.5rem;
        }

        .skills-section h3 {
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.75rem;
        }

        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .skill-tag {
            background-color: #f3f4f6;
            color: #374151;
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
        }

        .job-tabs {
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 1.5rem;
        }

        .job-tabs .tab-list {
            display: flex;
            gap: 1.5rem;
        }

        .job-tab {
            background: none;
            border: none;
            padding: 0.5rem 0;
            font-size: 1rem;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            position: relative;
            transition: color 0.2s;
        }

        .job-tab.active {
            color: #000;
        }

        .job-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: #000;
        }

        .job-tab:hover {
            color: #000;
        }

        .job-list {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .job-item {
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 1.5rem;
        }

        .job-item:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.25rem;
        }

        .job-title {
            color: var(--primary-blue);
            font-weight: 500;
            margin: 0;
        }

        .job-menu-btn {
            background: none;
            border: none;
            color: #6b7280;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .job-menu-btn:hover {
            background-color: #f3f4f6;
        }

        .job-rating {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;
        }

        .job-rating .stars {
            display: flex;
            color: #FBBF24;
        }

        .job-rating .rating-score {
            font-weight: 500;
        }

        .job-rating .separator {
            color: #6b7280;
        }

        .job-date {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.75rem;
        }

        .job-feedback {
            font-size: 0.875rem;
            color: #374151;
            margin-bottom: 0.75rem;
        }

        .job-earnings {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .job-total {
            font-weight: 500;
        }

        .job-rate, .job-hours {
            font-size: 0.875rem;
            color: #6b7280;
        }

        /* Side Notification Styles */
        .side-notification {
            position: fixed;
            bottom: 20px;
            right: -400px;
            width: 350px;
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            transition: right 0.4s ease-in-out;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .side-notification.show {
            right: 20px;
        }

        .notification-content {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            gap: 12px;
        }

        .notification-icon {
            flex-shrink: 0;
            width: 40px;
            height: 40px;
            background: transparent;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-blue);
            font-size: 18px;
        }

        .notification-text {
            flex: 1;
            color: #374151;
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
        }

        .notification-close {
            flex-shrink: 0;
            background: none;
            border: none;
            color: #9ca3af;
            font-size: 16px;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: color 0.2s ease;
        }

        .notification-close:hover {
            color: #6b7280;
            background: #f3f4f6;
        }

        /* Animation for the notification */
        @keyframes slideInRight {
            from {
                right: -400px;
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                right: 20px;
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideOutRight {
            from {
                right: 20px;
                opacity: 1;
                transform: translateY(0);
            }
            to {
                right: -400px;
                opacity: 0;
                transform: translateY(10px);
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="navbar-left">
                <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                    <div class="logo">
                        <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                        <h1>GigGenius</h1>
                    </div>
                </a>

                <div class="nav-links" id="navLinks">
                    <a href="{{ url_for('genius_page') }}">Find Gigs</a>
                    <a href="#" id="myApplicationsLink">My Applications</a>

                    <!-- Contracts Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Contracts
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Log Hours</a>
                            <a href="{{ url_for('landing_page') }}">Work Diary</a>
                        </div>
                    </div>

                    <!-- Earnings Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Earnings
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Billings and Earnings</a>
                            <a href="{{ url_for('landing_page') }}">Withdraw Earnings</a>
                            <a href="{{ url_for('landing_page') }}">Tax Info</a>
                        </div>
                    </div>

                    <a href="{{ url_for('messages') }}">Messages</a>
                </div>
            </div>
            <div class="right-section">
                <div class="search-container">
                    <div class="search-bar">
                        <input type="text" id="searchInput" placeholder="Search..." onkeypress="if(event.key==='Enter') performSearch()">
                        <i class="fas fa-search icon" onclick="performSearch()"></i>
                    </div>
                </div>
                <div class="auth-buttons">
                    <div class="notification-container">
                        <div class="notification-icon" id="notification-bell">
                            <i class="fas fa-bell"></i>
                            <span id="notification-count" class="notification-badge" style="display: none;">0</span>
                        </div>
                        <div class="notification-dropdown">
                            <div class="notification-header">
                                <span>Notifications</span>
                                <span class="notification-header-actions" id="mark-all-read">Mark all as read</span>
                            </div>
                            <div id="notification-list">
                                <!-- Notifications will be loaded here -->
                            </div>
                            <div id="empty-notifications" class="empty-notifications" style="display: none;">
                                <i class="far fa-bell-slash"></i>
                                <h3>No notifications yet</h3>
                                <p>You'll see application updates and important notifications here</p>
                            </div>
                        </div>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button">
                            <img id="navProfilePhoto" src="/api/profile-photo/genius/{{ genius.id }}" alt="Profile Picture" onerror="this.src='/static/img/default-avatar.png'">
                        </div>
                        <div class="profile-dropdown-content">
                            <a href="{{ url_for('genius_profile') }}">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-cog"></i> Account Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('logout') }}" class="logout-option" style="color: #dc3545 !important; font-weight: bold;">
                                <i class="fas fa-sign-out-alt"></i> Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>




    <!-- Main Content -->
    <div class="body-container">
        <!-- Main Content -->
        <main class="main-content">
            <!-- Profile Section -->
            <section class="profile-section">
                <!-- Name and Title above video -->
                <div class="profile-header">
                    <h1 class="profile-name" id="profileName">{{ genius.first_name }} {{ genius.last_name }}</h1>
                    <p class="profile-title">
                        <i class="fas fa-briefcase"></i>
                        {% if genius.position %}
                            <span id="profilePosition">{{ genius.position }}</span>
                        {% endif %}
                    </p>
                </div>
                
                <div class="profile-row">
                    <!-- Video Card -->
                    <div class="video-card">
                        <div class="video-container" id="videoContainer">
                            <!-- Video Upload Placeholder (shown when no video) -->
                            <div class="video-placeholder" id="videoPlaceholder" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px;">
                                <div style="text-align: center; color: #6c757d;">
                                    <i class="fas fa-video" style="font-size: 3rem; margin-bottom: 1rem; color: #adb5bd;"></i>
                                    <h3 style="margin: 0 0 0.5rem 0; font-size: 1.25rem; font-weight: 600;">Add a video introduction</h3>
                                    <p style="margin: 0 0 1rem 0; font-size: 0.875rem;">Show clients who you are with a video introduction</p>
                                    <p style="margin: 0 0 1.5rem 0; font-size: 0.75rem; color: #adb5bd;">📁 Drag & drop video here or click to select</p>
                                    <button class="upload-video-btn" id="uploadVideoBtn" style="background: var(--primary-blue); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; font-weight: 600; margin-right: 0.5rem;">
                                        <i class="fas fa-upload"></i> Select Video
                                    </button>
                                    <input type="file" id="videoUpload" accept="video/*" style="display: none;">
                                </div>
                            </div>

                            <!-- Video Preview (shown when video is selected but not saved) -->
                            <div class="video-preview" id="videoPreview" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: none;">
                                <video id="previewVideo" controls style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 100%; height: 100%; object-fit: cover;">
                                    <source src="#" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                                <div style="position: absolute; bottom: 10px; right: 10px; display: flex; gap: 0.5rem;">
                                    <button id="saveVideoBtn" style="background: var(--primary-blue); color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; font-weight: 600;">
                                        <i class="fas fa-save"></i> Save Video
                                    </button>
                                    <button id="cancelVideoBtn" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; font-weight: 600;">
                                        <i class="fas fa-times"></i> Cancel
                                    </button>
                                </div>
                            </div>

                            <!-- Video Player (shown when video exists) -->
                            <div class="video-player" id="videoPlayer" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: none;">
                                <video id="profileVideo" controls poster="https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=450&fit=crop" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 100%; height: 100%; object-fit: cover;">
                                    <source src="#" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>

                                <!-- Video Controls -->
                                <div class="video-controls" style="position: absolute; top: 10px; right: 10px; display: flex; gap: 0.5rem;">
                                    <button class="video-control-btn" id="deleteVideoBtn" style="background: rgba(220, 53, 69, 0.9); color: white; border: none; padding: 0.5rem; border-radius: 4px; cursor: pointer;" title="Delete Video">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button class="video-control-btn" id="replaceVideoBtn" style="background: rgba(40, 167, 69, 0.9); color: white; border: none; padding: 0.5rem; border-radius: 4px; cursor: pointer;" title="Replace Video">
                                        <i class="fas fa-upload"></i>
                                    </button>
                                </div>

                                <!-- Fullscreen Button -->
                                <button class="expand-btn" id="expandBtn" aria-label="Expand video" style="position: absolute; bottom: 10px; right: 10px; background: rgba(0, 0, 0, 0.7); color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
                                    <i class="fas fa-expand"></i> Fullscreen
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Card -->
                    <div class="profile-card">
                        <div class="profile-content">
                            <!-- Stats - Updated to include 4 items -->
                            <div class="profile-stats">
                                <div class="stat-item">
                                    <div class="stat-value" id="hourlyRate">
                                        {% if genius.hourly_rate %}
                                            ${{ genius.hourly_rate }}
                                        {% else %}
                                            <span style="color:#bbb;">No rate set</span>
                                        {% endif %}
                                    </div>
                                    <div class="stat-label">Hourly Rate</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="totalEarnings">$0</div>
                                    <div class="stat-label">Total Earnings</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="hired">0</div>
                                    <div class="stat-label">Hired</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="completedJobs">0</div>
                                    <div class="stat-label">Completed</div>
                                </div>
                            </div>

                            <!-- Professional Summary -->
                            <div class="profile-summary">
                                <div class="flex items-center justify-between mb-4">
                                    <h3><i class="fas fa-user-circle"></i> Professional Summary</h3>
                                    <button class="edit-btn" id="editSummaryBtn">
                                        <i class="fas fa-edit"></i>
                                        <span>Edit</span>
                                    </button>
                                </div>
                                <p class="summary-text" id="summaryText">
                                    {% if genius.professional_sum %}
                                        {{ genius.professional_sum }}
                                    {% else %}
                                        <span style="color: #6b7280; font-style: italic;">
                                            Add a professional summary to showcase your skills and experience to potential clients.
                                            Click the Edit button to get started.
                                        </span>
                                    {% endif %}
                                </p>
                                <span class="show-more hidden" id="showMore"><i class="fas fa-plus-circle"></i> Show More</span>
                            </div>

                            <!-- Profile Fields -->
                            <div class="profile-fields">
                                <div class="field-group">
                                    <label>Availability</label>
                                    <input type="text" value="{% if genius.availability == 'fulltime' %}Full-Time{% elif genius.availability == 'parttime' %}Part-Time{% else %}{{ genius.availability if genius.availability else '' }}{% endif %}" readonly class="field-input" id="displayAvailability">
                                </div>
                                <div class="field-group">
                                    <label>Language</label>
                                    <input type="text" value="{{ genius.language if genius.language else 'English' }}" readonly class="field-input" id="displayLanguage">
                                </div>
                                <div class="field-group">
                                    <label>Country</label>
                                    <input type="text" value="{{ genius.country if genius.country else '' }}" readonly class="field-input" id="displayCountryField">
                                </div>
                            </div>

                            <!-- Edit Profile Button -->
                            <button class="edit-profile-btn" id="editProfileBtn">
                                <i class="fas fa-user-edit"></i>
                                <span>Edit Profile</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Introduction/Portfolio and Work History Section -->
            <section class="portfolio-section">
                <div class="section-row">
                    <!-- Introduction and Portfolio -->
                    <div class="section-card">
                        <!-- Introduction -->
                        <div class="section-content">
                            <div class="introduction-header">
                                <h2 class="section-title" id="introduction"><i class="fas fa-info-circle"></i> Introduction</h2>
                                <button class="edit-btn" id="editIntroductionBtn">
                                    <i class="fas fa-edit"></i>
                                    <span>Edit</span>
                                </button>
                            </div>
                            <textarea class="textarea" id="introductionDisplay" placeholder="Write your introduction not less than 300 words..." readonly>{{ genius.introduction if genius.introduction else '' }}</textarea>
                        </div>

                        <!-- Portfolio Header -->
                        <div class="portfolio-header">
                            <h2 class="portfolio-title">Portfolio</h2>
                            <div class="portfolio-actions">
                                <button class="portfolio-add-btn" id="portfolioAddBtn">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="portfolio-refresh-btn">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Portfolio Tabs -->
                        <div class="portfolio-tabs">
                            <button class="portfolio-tab active" data-tab="published">Published</button>
                            <button class="portfolio-tab" data-tab="drafts">Drafts</button>
                        </div>

                    <!-- Portfolio Grid -->
                    <div class="portfolio-grid" id="portfolioGrid">
                        <!-- Published Projects (shown by default) -->
                        <div class="portfolio-content" id="publishedContent">
                            {% if genius.portfolio and genius.portfolio.published and genius.portfolio.published|length > 0 %}
                                {% for project in genius.portfolio.published %}
                                    <div class="portfolio-card clickable-card" data-project-id="{{ project.id }}" data-project-title="{{ project.project_title }}" data-project-role="{{ project.project_role or '' }}" data-project-description="{{ project.project_description }}" data-project-content="{{ project.project_content or '' }}" data-project-skills="{{ project.skills_and_deliverables or '' }}" data-related-job="{{ project.related_giggenius_job or '' }}" data-has-image="{{ 'true' if project.project_image_filename else 'false' }}" style="border: 1px solid #ddd; border-radius: 8px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden; cursor: pointer; transition: transform 0.2s, box-shadow 0.2s;">
                                        <!-- Project Image -->
                                        <div style="width: 100%; height: 150px; overflow: hidden; position: relative;">
                                            {% if project.project_image_filename %}
                                                <img src="{{ url_for('api_portfolio_image', project_id=project.id) }}" alt="{{ project.project_title }}" style="width: 100%; height: 100%; object-fit: cover;">
                                            {% else %}
                                                <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">
                                                    <i class="fas fa-image"></i>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <!-- Project Title -->
                                        <div class="portfolio-card-content" style="padding: 1rem; text-align: center;">
                                            <h3 style="margin: 0; font-size: 1.1rem; font-weight: 600; color: #004AAD;">{{ project.project_title }}</h3>
                                        </div>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div style="padding:2rem;text-align:center;color:#888;">No published projects yet. Click + to add your first project.</div>
                            {% endif %}
                        </div>

                        <!-- Draft Projects (hidden by default) -->
                        <div class="portfolio-content" id="draftsContent" style="display: none;">
                            {% if genius.portfolio and genius.portfolio.drafts and genius.portfolio.drafts|length > 0 %}
                                {% for project in genius.portfolio.drafts %}
                                    <div class="portfolio-card clickable-card" data-project-id="{{ project.id }}" data-project-title="{{ project.project_title }}" data-project-role="{{ project.project_role or '' }}" data-project-description="{{ project.project_description }}" data-project-content="{{ project.project_content or '' }}" data-project-skills="{{ project.skills_and_deliverables or '' }}" data-related-job="{{ project.related_giggenius_job or '' }}" data-has-image="{{ 'true' if project.project_image_filename else 'false' }}" data-status="draft" style="border: 1px solid #ddd; border-radius: 8px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden; cursor: pointer; transition: transform 0.2s, box-shadow 0.2s;">
                                        <!-- Project Image -->
                                        <div style="width: 100%; height: 150px; overflow: hidden; position: relative;">
                                            {% if project.project_image_filename %}
                                                <img src="{{ url_for('api_portfolio_image', project_id=project.id) }}" alt="{{ project.project_title }}" style="width: 100%; height: 100%; object-fit: cover;">
                                            {% else %}
                                                <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #f57c00 0%, #ff9800 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">
                                                    <i class="fas fa-image"></i>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <!-- Project Title -->
                                        <div class="portfolio-card-content" style="padding: 1rem; text-align: center;">
                                            <h3 style="margin: 0 0 0.5rem 0; font-size: 1.1rem; font-weight: 600; color: #004AAD;">{{ project.project_title }}</h3>
                                            <span style="background: #ffecb3; color: #f57c00; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.7rem;">Draft</span>
                                        </div>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div style="padding:2rem;text-align:center;color:#888;">No draft projects yet.</div>
                            {% endif %}
                        </div>
                    </div>

                        <!-- Portfolio Pagination (keep or remove as needed) -->
                        <div class="portfolio-pagination">
                            <button class="pagination-btn prev">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="pagination-btn active">1</button>
                            <button class="pagination-btn">2</button>
                            <button class="pagination-btn">3</button>
                            <button class="pagination-btn">4</button>
                            <span class="pagination-dots">...</span>
                            <button class="pagination-btn next">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Work History -->
                    <div class="section-card">
                        <div class="section-content">
                            <h2 class="text-lg font-semibold mb-6">Work History</h2>

                            <!-- Work History Summary -->
                            <div class="work-history-summary">
                                {% if genius.work_history and genius.work_history|length > 0 %}
                                    {# Render work history summary dynamically #}
                                {% else %}
                                    <p style="color:#888;">No work history yet.</p>
                                {% endif %}
                            </div>

                            <!-- Skills Used -->
                            <div class="skills-section">
                                <h3>Skills used in past work</h3>
                                <div class="skills-list">
                                    <span class="skill-tag">JavaScript</span>
                                    <span class="skill-tag">React</span>
                                    <span class="skill-tag">Node.js</span>
                                    <span class="skill-tag">Python</span>
                                    <span class="skill-tag">Digital Marketing</span>
                                    <span class="skill-tag">SEO</span>
                                    <span class="skill-tag">UI/UX Design</span>
                                    <span class="skill-tag">Database Design</span>
                                </div>
                            </div>

                            <!-- Job Tabs -->
                            <div class="job-tabs">
                                <div class="tab-list">
                                    <button class="job-tab active" data-tab="completed">Completed jobs (12)</button>
                                    <button class="job-tab" data-tab="in-progress">In progress (3)</button>
                                </div>
                            </div>

                            <!-- Job List -->
                            <div class="job-list">
                                {% if genius.jobs and genius.jobs|length > 0 %}
                                    {% for job in genius.jobs %}
                                        <div class="job-item">
                                            <!-- Render job info here -->
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div style="padding:2rem;text-align:center;color:#888;">No jobs to display yet.</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Side Notification -->
    <div class="side-notification" id="sideNotification">
        <div class="notification-content">
            <div class="notification-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="notification-text">
                <span id="notificationMessage">Professional summary has been updated</span>
            </div>
            <button class="notification-close" id="notificationClose">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Modal for Professional Summary -->
    <div class="modal" id="professionalSummaryModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body">
                <h2 class="modal-title">Edit Professional Summary *</h2>
                <textarea class="modal-textarea" id="professionalSummaryTextarea" placeholder="Write something..."></textarea>
                <div class="modal-buttons">
                    <button class="modal-back-btn" id="summaryBackBtn">Back</button>
                    <button class="modal-next-btn" id="summaryNextBtn">Next</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Introduction -->
    <div class="modal" id="introductionModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body">
                <h2 class="modal-title">Edit Introduction *</h2>
                <textarea class="modal-textarea" id="introductionTextarea" placeholder="Write something...">{{ genius.introduction or '' }}</textarea>
                <div class="modal-buttons">
                    <button class="modal-back-btn" id="introBackBtn">Back</button>
                    <button class="modal-next-btn" id="introNextBtn">Next</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Edit Profile -->
    <div class="modal" id="editProfileModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <div class="modal-body">
                <div class="welcome-section" style="margin-bottom: 2rem;">
                    <h1 class="section-title" style="margin-bottom: 1rem;">Welcome <span style="color: var(--primary-pink);">{{ genius.first_name }} {{ genius.last_name }}</span></h1>
                    <div class="profile-info-display" style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div class="info-item" style="display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-briefcase" style="color: var(--primary-blue);"></i>
                            <span id="displayPosition" style="font-weight: 500;">{{ genius.position }}</span>
                        </div>
                        <div class="info-item" style="display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-map-marker-alt" style="color: var(--primary-blue);"></i>
                            <span id="displayCountry" style="font-weight: 500;">{{ genius.country }}</span>
                        </div>
                    </div>
                </div>

                <div class="profile-form">
                    <!-- Profile Photo Section -->
                    <div class="profile-photo-section">
                        <div class="profile-photo">
                            <img id="currentProfilePhoto" src="/api/profile-photo/genius/{{ genius.id }}" alt="Current Profile Photo" onerror="this.src='/static/img/default-avatar.png'">
                        </div>
                        <label for="profile-upload" class="upload-btn">
                            <i class="fas fa-camera"></i>
                            <span>Change Photo</span>
                        </label>
                        <input type="file" id="profile-upload" accept="image/*" style="display: none;">
                        <p class="upload-note">Maximum of 2MB</p>
                    </div>

                    <!-- Profile Details Section -->
                    <div class="profile-details-section">
                        <h2 class="section-title">Edit your profile</h2>

                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" class="form-control" value="{{ genius.email or '' }}" readonly>
                        </div>

                        <div class="form-group">
                            <label for="mobile">Mobile No.</label>
                            <input type="tel" id="mobile" class="form-control" value="{{ genius.mobile or '' }}">
                        </div>

                        <div class="form-group">
                            <label for="position">Position</label>
                            <input type="text" id="position" class="form-control" value="{{ genius.position or '' }}">
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label for="expertise">Expertise Level</label>
                                    <select id="expertise" class="form-control">
                                        <option value="Expert" {% if genius.expertise == 'Expert' %}selected{% endif %}>Expert</option>
                                        <option value="Intermediate" {% if genius.expertise == 'Intermediate' %}selected{% endif %}>Intermediate</option>
                                        <option value="Beginner" {% if genius.expertise == 'Beginner' %}selected{% endif %}>Beginner</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label for="rate">Rate per Hour (USD)</label>
                                    <input type="number" id="rate" class="form-control" value="{{ genius.hourly_rate or '' }}">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="availability">Availability</label>
                            <select id="availability" class="form-control">
                                <option value="fulltime" {% if genius.availability == 'fulltime' %}selected{% endif %}>Full-Time</option>
                                <option value="parttime" {% if genius.availability == 'parttime' %}selected{% endif %}>Part-Time</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="country">Country</label>
                            <select id="country" class="form-control">
                                <option value="">Select Country</option>
                                <option value="Philippines" {% if genius.country == 'Philippines' %}selected{% endif %}>Philippines</option>
                                <option value="Nigeria" {% if genius.country == 'Nigeria' %}selected{% endif %}>Nigeria</option>
                                <option value="United States" {% if genius.country == 'United States' %}selected{% endif %}>United States</option>
                                <option value="United Kingdom" {% if genius.country == 'United Kingdom' %}selected{% endif %}>United Kingdom</option>
                                <option value="Canada" {% if genius.country == 'Canada' %}selected{% endif %}>Canada</option>
                                <option value="Australia" {% if genius.country == 'Australia' %}selected{% endif %}>Australia</option>
                                <option value="Germany" {% if genius.country == 'Germany' %}selected{% endif %}>Germany</option>
                                <option value="France" {% if genius.country == 'France' %}selected{% endif %}>France</option>
                                <option value="Japan" {% if genius.country == 'Japan' %}selected{% endif %}>Japan</option>
                                <option value="China" {% if genius.country == 'China' %}selected{% endif %}>China</option>
                                <option value="India" {% if genius.country == 'India' %}selected{% endif %}>India</option>
                                <option value="Brazil" {% if genius.country == 'Brazil' %}selected{% endif %}>Brazil</option>
                                <option value="South Africa" {% if genius.country == 'South Africa' %}selected{% endif %}>South Africa</option>
                                <!-- Add a fallback option for any other country value -->
                                {% if genius.country and genius.country not in ['Philippines', 'Nigeria', 'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 'Japan', 'China', 'India', 'Brazil', 'South Africa'] %}
                                <option value="{{ genius.country }}" selected>{{ genius.country }}</option>
                                {% endif %}
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="language">Language</label>
                            <select id="language" class="form-control">
                                <option value="English" selected>English</option>
                                <option value="French">French</option>
                                <option value="Spanish">Spanish</option>
                                <option value="German">German</option>
                                <option value="Chinese">Chinese</option>
                                <option value="Japanese">Japanese</option>
                                <option value="Arabic">Arabic</option>
                                <option value="Russian">Russian</option>
                                <option value="Portuguese">Portuguese</option>
                                <option value="Hindi">Hindi</option>
                            </select>
                        </div>

                        <div class="modal-buttons" style="text-align: right;">
                            <button class="modal-back-btn" id="profileBackBtn">Back</button>
                            <button class="modal-next-btn" id="saveProfileBtn">Next</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Portfolio View/Publish -->
    <div class="modal" id="portfolioViewModal">
        <div class="modal-content" style="max-width: 1400px; width: 95%; max-height: 90vh; overflow: hidden;"
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content" style="display: flex; justify-content: space-between; align-items: center; padding: 1rem 2rem;">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <button class="copy-link-btn" style="background: #f0f0f0; border: 1px solid #ddd; padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer; font-size: 0.9rem; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-link"></i> Copy link
                        </button>
                        <button class="modal-close-btn" id="portfolioViewCloseBtn" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #666;">×</button>
                    </div>
                </div>
            </header>

            <!-- Portfolio Content -->
            <div class="modal-body" style="padding: 2rem; height: calc(90vh - 120px); overflow: hidden;">
                <div class="portfolio-view-container" style="height: 100%;">
                    <!-- Project Header with Title and Images Side by Side -->
                    <div class="project-header-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; height: 100%; align-items: start;">
                        <!-- Left Side - Project Info and Details (Fixed, No Scroll) -->
                        <div class="project-info" style="overflow: hidden;">
                            <h1 class="project-title" style="font-size: 2rem; font-weight: 700; color: #333; margin-bottom: 0.5rem;">Sample Project Title</h1>
                            <div class="project-meta" style="display: flex; align-items: center; gap: 1rem; margin-bottom: 2rem;">
                                <span class="project-role" style="color: #666; font-size: 1rem;">My role: <strong>Full Stack Developer</strong></span>
                                <span class="project-date" style="color: #666; font-size: 0.9rem;">Published on Feb 24, 2025</span>
                            </div>

                            <!-- Project Details moved here -->
                            <div class="project-section" style="margin-bottom: 2rem;">
                                <h3 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: #333;">Project description</h3>
                                <p class="project-description" style="color: #666; line-height: 1.6; font-size: 0.95rem;">
                                    This is a comprehensive web development project that involved creating a modern, responsive website with advanced functionality. The project included both frontend and backend development, database design, and deployment.
                                </p>
                            </div>

                            <div class="project-section" style="margin-bottom: 2rem;">
                                <h3 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: #333;">Skills and Deliverables</h3>
                                <div class="skills-tags" style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                                    <span class="skill-tag" style="background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;">Web Development</span>
                                    <span class="skill-tag" style="background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;">React.js</span>
                                    <span class="skill-tag" style="background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;">Node.js</span>
                                    <span class="skill-tag" style="background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;">Database Design</span>
                                </div>
                            </div>

                            <div class="project-section related-job-section" style="margin-bottom: 2rem; display: none;">
                                <h3 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: #333;">Related GigGenius Job</h3>
                                <p class="related-job-text" style="color: #666; line-height: 1.6; font-size: 0.95rem;">
                                    <!-- Related job will be populated here -->
                                </p>
                            </div>

                            <div class="project-actions" style="margin-top: 2rem;">
                                <button class="report-btn" style="background: none; border: none; color: #666; font-size: 0.9rem; cursor: pointer; text-decoration: underline;">
                                    Report an issue
                                </button>
                            </div>
                        </div>

                        <!-- Right Side - Project Images (Scrollable Only) -->
                        <div class="project-content-container" style="margin: 0; height: 100%; overflow-y: auto; overflow-x: hidden; padding-right: 1rem;">
                            <div class="project-section content-section" style="margin-bottom: 0; display: none;">
                                <div class="project-content" style="color: #666; line-height: 1.6; font-size: 0.95rem;">
                                    <!-- Project content will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Certification Edit -->
    <div class="modal" id="certificationEditModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body">
                <h2 class="modal-title">Edit Certification *</h2>
                <textarea class="modal-textarea" placeholder="Edit your certification details..."></textarea>
                <div class="modal-buttons">
                    <button class="modal-back-btn" id="certificationBackBtn">Back</button>
                    <button class="modal-next-btn">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Portfolio Add (Upwork Style) -->
    <div class="modal" id="portfolioAddModal">
        <div class="modal-content" style="max-width: 800px;">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content" style="justify-content: space-between;">
                    <h2 style="margin: 0; font-size: 1.5rem; font-weight: 600; color: #004AAD;">Add a new portfolio project</h2>
                    <button class="modal-close-btn" id="portfolioAddCloseBtn" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #666;">×</button>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body" style="padding: 2rem;">
                <p style="color: #666; margin-bottom: 2rem; font-size: 0.9rem;">All fields are required unless otherwise indicated</p>

                <div class="upwork-form">
                    <!-- Project Title -->
                    <div class="form-group" style="margin-bottom: 2rem;">
                        <label for="projectTitle" style="display: block; font-weight: 600; margin-bottom: 0.5rem; color: #333;">Project title</label>
                        <input type="text" id="projectTitle" class="form-control" placeholder="Enter a brief but descriptive title" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px; font-size: 1rem;">
                        <div style="text-align: right; font-size: 0.8rem; color: #666; margin-top: 0.25rem;">70 characters left</div>
                    </div>

                    <!-- Your Role -->
                    <div class="form-group" style="margin-bottom: 2rem;">
                        <label for="projectRole" style="display: block; font-weight: 600; margin-bottom: 0.5rem; color: #333;">Your role <span style="color: #666; font-weight: normal;">(optional)</span></label>
                        <input type="text" id="projectRole" class="form-control" placeholder="e.g., Front-end engineer or Marketing analyst" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px; font-size: 1rem;">
                        <div style="text-align: right; font-size: 0.8rem; color: #666; margin-top: 0.25rem;">100 characters left</div>
                    </div>

                    <!-- Project Description -->
                    <div class="form-group" style="margin-bottom: 2rem;">
                        <label for="projectDescription" style="display: block; font-weight: 600; margin-bottom: 0.5rem; color: #333;">Project description</label>
                        <textarea id="portfolioContent" class="form-control" placeholder="Briefly describe the project's goals, your solution and the impact you made here." style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px; font-size: 1rem; min-height: 120px; resize: vertical; font-family: inherit;"></textarea>
                        <div style="display: flex; align-items: center; gap: 0.5rem; margin-top: 0.5rem;">
                            <i class="fas fa-exclamation-circle" style="color: #e74c3c; font-size: 0.8rem;"></i>
                            <span style="color: #e74c3c; font-size: 0.8rem;">Description is required</span>
                        </div>
                    </div>

                    <!-- Add Project Content -->
                    <div class="form-group" style="margin-bottom: 2rem;">
                        <label style="display: block; font-weight: 600; margin-bottom: 0.5rem; color: #333;">Add Project content</label>
                        <div style="border: 2px dashed #004AAD; border-radius: 8px; padding: 3rem; text-align: center; background: #f8f9fa; position: relative;">
                            <div style="display: flex; justify-content: center; gap: 1rem; margin-bottom: 1rem;">
                                <button type="button" id="uploadImageBtn" title="Upload Image" style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid #004AAD; background: white; color: #004AAD; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-image"></i>
                                </button>
                                <button type="button" id="portfolioUploadVideoBtn" title="Upload Video" style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid #004AAD; background: white; color: #004AAD; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-video"></i>
                                </button>
                                <button type="button" id="addTextBlockBtn" title="Add Text Block" style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid #004AAD; background: white; color: #004AAD; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-font"></i>
                                </button>
                                <button type="button" id="addLinkBtn" title="Add Link" style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid #004AAD; background: white; color: #004AAD; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-link"></i>
                                </button>
                                <button type="button" id="uploadPdfBtn" title="Upload PDF" style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid #004AAD; background: white; color: #004AAD; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-file-pdf"></i>
                                </button>
                                <button type="button" id="addMusicBtn" title="Add Music" style="width: 40px; height: 40px; border-radius: 50%; border: 2px solid #004AAD; background: white; color: #004AAD; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-music"></i>
                                </button>
                            </div>
                            <p style="color: #666; margin: 0; font-size: 1rem;">Add content</p>
                            <!-- Content will be dynamically added here -->
                            <div id="portfolioProjectContent" style="margin-top: 1rem; text-align: left;"></div>
                        </div>
                        <div style="text-align: right; font-size: 0.8rem; color: #666; margin-top: 0.25rem;">600 characters left</div>
                    </div>

                    <!-- Skills and Deliverables -->
                    <div class="form-group" style="margin-bottom: 2rem;">
                        <label for="projectSkills" style="display: block; font-weight: 600; margin-bottom: 0.5rem; color: #333;">Skills and deliverables</label>
                        <input type="text" id="projectSkills" class="form-control" placeholder="Type to add skills relevant to this project" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px; font-size: 1rem;">
                        <div style="text-align: right; font-size: 0.8rem; color: #666; margin-top: 0.25rem;">5 skills left</div>
                    </div>

                    <!-- Related Upwork Job -->
                    <div class="form-group" style="margin-bottom: 2rem;">
                        <label for="relatedJob" style="display: block; font-weight: 600; margin-bottom: 0.5rem; color: #333;">Related GigGenius job <span style="color: #666; font-weight: normal;">(optional)</span></label>
                        <input type="text" id="relatedJob" class="form-control" placeholder="Search a related job" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px; font-size: 1rem;">
                    </div>
                </div>

                <div class="modal-buttons" style="display: flex; justify-content: flex-end; gap: 1rem; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #eee;">
                    <button class="modal-back-btn" id="portfolioAddBackBtn" style="background: none; border: 1px solid #ddd; color: #666; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer;">Save as draft</button>
                    <button class="modal-next-btn" id="portfolioAddSaveBtn" style="background: #CD208B; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; font-weight: 600;">Next: Preview</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Portfolio Reorder -->
    <div class="modal" id="portfolioReorderModal">
        <div class="modal-content" style="max-width: 600px;">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content" style="justify-content: space-between;">
                    <h2 style="margin: 0; font-size: 1.5rem; font-weight: 600;">Reorder portfolio projects</h2>
                    <button class="modal-close-btn" id="portfolioReorderCloseBtn" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #666;">×</button>
                </div>
            </header>

            <!-- Reorder Section -->
            <div class="modal-body" style="padding: 2rem;">
                <div class="reorder-list">
                    <!-- Portfolio Item 1 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Bryan's Handyman & Construction LLC</h4>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 2 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Virtual Assistant Services</h4>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 3 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Graphics Design and Web Development</h4>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 4 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">GigGenius Web and App Development</h4>
                            <div style="background: #28a745; color: white; padding: 0.2rem 0.5rem; border-radius: 4px; font-size: 0.75rem; display: inline-block; margin-top: 0.25rem;">Reorder portfolio projects</div>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 5 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">Bryan's Handyman and Construction LLC</h4>
                            <div style="color: #666; font-size: 0.875rem;">April 2024</div>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Portfolio Item 6 -->
                    <div class="reorder-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px; margin-bottom: 1rem; background: white;">
                        <div class="drag-handle" style="margin-right: 1rem; cursor: grab; color: #999;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="project-thumbnail" style="width: 60px; height: 40px; border-radius: 4px; overflow: hidden; margin-right: 1rem;">
                            <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=250&fit=crop" alt="Project" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="project-info" style="flex: 1;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: #333;">SMB Paralegal Services</h4>
                            <div style="color: #666; font-size: 0.875rem;">April 2024</div>
                        </div>
                        <div class="reorder-controls" style="display: flex; flex-direction: column; gap: 0.25rem;">
                            <button class="move-up-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="move-down-btn" style="background: none; border: none; color: #666; cursor: pointer; padding: 0.25rem;">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="modal-buttons" style="display: flex; justify-content: flex-end; gap: 1rem; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #eee;">
                    <button class="modal-back-btn" id="portfolioReorderCancelBtn" style="background: none; border: 1px solid #ddd; color: #666; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer;">Cancel</button>
                    <button class="modal-next-btn" id="portfolioReorderSaveBtn" style="background: #28a745; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; font-weight: 600;">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Unified Portfolio Notification System
        function showPortfolioNotification(message, type = 'success', icon = 'fas fa-check-circle') {
            // Remove any existing portfolio notifications
            const existingNotifications = document.querySelectorAll('.portfolio-notification');
            existingNotifications.forEach(notification => notification.remove());

            const notification = document.createElement('div');
            notification.className = 'portfolio-notification';

            // Set colors based on type
            let backgroundColor, borderColor, iconColor;
            switch(type) {
                case 'success':
                    backgroundColor = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
                    borderColor = '#10b981';
                    iconColor = '#ffffff';
                    break;
                case 'error':
                    backgroundColor = 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)';
                    borderColor = '#ef4444';
                    iconColor = '#ffffff';
                    break;
                case 'info':
                    backgroundColor = 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)';
                    borderColor = '#3b82f6';
                    iconColor = '#ffffff';
                    break;
                default:
                    backgroundColor = 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)';
                    borderColor = '#6b7280';
                    iconColor = '#ffffff';
            }

            notification.style.cssText = `
                position: fixed;
                top: 30px;
                right: 30px;
                background: ${backgroundColor};
                color: white;
                padding: 20px 25px;
                border-radius: 16px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px ${borderColor}20;
                z-index: 10000;
                font-family: 'Poppins', sans-serif;
                font-size: 15px;
                font-weight: 600;
                max-width: 400px;
                min-width: 300px;
                opacity: 0;
                transform: translateX(100%) scale(0.8);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                display: flex;
                align-items: center;
                gap: 15px;
            `;

            notification.innerHTML = `
                <div style="
                    width: 40px;
                    height: 40px;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                ">
                    <i class="${icon}" style="color: ${iconColor}; font-size: 18px;"></i>
                </div>
                <div style="flex: 1;">
                    <div style="font-weight: 700; margin-bottom: 4px; font-size: 16px;">
                        ${type === 'success' ? 'Success!' : type === 'error' ? 'Error!' : 'Info'}
                    </div>
                    <div style="font-weight: 500; opacity: 0.95; line-height: 1.4;">
                        ${message}
                    </div>
                </div>
                <button onclick="this.parentElement.remove()" style="
                    background: none;
                    border: none;
                    color: white;
                    cursor: pointer;
                    padding: 8px;
                    border-radius: 50%;
                    opacity: 0.7;
                    transition: all 0.2s ease;
                    width: 32px;
                    height: 32px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                " onmouseover="this.style.opacity='1'; this.style.background='rgba(255,255,255,0.2)'" onmouseout="this.style.opacity='0.7'; this.style.background='none'">
                    <i class="fas fa-times" style="font-size: 14px;"></i>
                </button>
            `;

            document.body.appendChild(notification);

            // Show notification with animation
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0) scale(1)';
            }, 10);

            // Auto-hide after 5 seconds for success, 7 seconds for error
            const duration = type === 'error' ? 7000 : 5000;
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.opacity = '0';
                    notification.style.transform = 'translateX(100%) scale(0.8)';
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 400);
                }
            }, duration);

            return notification;
        }

        // Side Notification Functions
        function showSideNotification(message = 'Professional summary has been updated') {
            const notification = document.getElementById('sideNotification');
            const messageElement = document.getElementById('notificationMessage');

            if (notification && messageElement) {
                messageElement.textContent = message;
                notification.classList.add('show');

                // Auto hide after 4 seconds
                setTimeout(() => {
                    hideSideNotification();
                }, 4000);
            }
        }

        function hideSideNotification() {
            const notification = document.getElementById('sideNotification');
            if (notification) {
                notification.classList.remove('show');
            }
        }

        // Close button functionality
        document.addEventListener('DOMContentLoaded', function() {
            const closeBtn = document.getElementById('notificationClose');
            if (closeBtn) {
                closeBtn.addEventListener('click', hideSideNotification);
            }
        });
    </script>
    <script>
        // Show More functionality for summary text
        const summaryText = document.getElementById('summaryText');
        const showMoreBtn = document.getElementById('showMore');
        const editSummaryBtn = document.getElementById('editSummaryBtn');

        showMoreBtn.addEventListener('click', () => {
            summaryText.textContent = "Collapsible text is perfect for longer content like paragraphs and descriptions. It's a great way to give people more information while keeping your layout clean.";
            showMoreBtn.style.display = 'none';
            // Edit button is already visible
        });

        // Video functionality
        const videoContainer = document.getElementById('videoContainer');
        const videoPlaceholder = document.getElementById('videoPlaceholder');
        const videoPreview = document.getElementById('videoPreview');
        const videoPlayer = document.getElementById('videoPlayer');
        const uploadVideoBtn = document.getElementById('uploadVideoBtn');
        const videoUpload = document.getElementById('videoUpload');
        const previewVideo = document.getElementById('previewVideo');
        const saveVideoBtn = document.getElementById('saveVideoBtn');
        const cancelVideoBtn = document.getElementById('cancelVideoBtn');
        const deleteVideoBtn = document.getElementById('deleteVideoBtn');
        const replaceVideoBtn = document.getElementById('replaceVideoBtn');
        const expandBtn = document.getElementById('expandBtn');
        const video = document.getElementById('profileVideo');

        // Check if there's an existing video from the database
        let hasVideo = false;
        let selectedVideoFile = null;

        // Show appropriate view based on video existence
        function updateVideoView() {
            if (hasVideo) {
                videoPlaceholder.style.display = 'none';
                videoPreview.style.display = 'none';
                videoPlayer.style.display = 'block';
            } else if (selectedVideoFile) {
                videoPlaceholder.style.display = 'none';
                videoPreview.style.display = 'block';
                videoPlayer.style.display = 'none';
            } else {
                videoPlaceholder.style.display = 'flex';
                videoPreview.style.display = 'none';
                videoPlayer.style.display = 'none';
            }
        }

        // Check for existing video when page loads
        async function checkExistingVideo() {
            try {
                const response = await fetch(`/api/profile-video/genius/{{ genius.id }}`);
                if (response.ok) {
                    // Video exists, set up the video player
                    hasVideo = true;
                    video.src = `/api/profile-video/genius/{{ genius.id }}?t=${new Date().getTime()}`;
                    console.log('✅ Existing profile video found');
                } else {
                    // No video found
                    hasVideo = false;
                    console.log('ℹ️ No profile video found');
                }
            } catch (error) {
                console.log('ℹ️ No profile video found or error checking:', error);
                hasVideo = false;
            }
            updateVideoView();
        }

        // Initialize video view
        checkExistingVideo();

        // Upload video functionality
        if (uploadVideoBtn && videoUpload) {
            uploadVideoBtn.addEventListener('click', () => {
                videoUpload.click();
            });

            videoUpload.addEventListener('change', (event) => {
                const file = event.target.files[0];
                if (file && file.type.startsWith('video/')) {
                    // Validate file size (50MB max)
                    const maxSize = 50 * 1024 * 1024; // 50MB
                    if (file.size > maxSize) {
                        showSideNotification('Video file size must be less than 50MB. Current size: ' + (file.size / (1024 * 1024)).toFixed(2) + 'MB');
                        return;
                    }

                    // Validate video format
                    const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm', 'video/mkv'];
                    if (!allowedTypes.includes(file.type)) {
                        showSideNotification('Please select a supported video format: MP4, AVI, MOV, WMV, WebM, or MKV');
                        return;
                    }

                    selectedVideoFile = file;

                    // Show preview
                    const videoURL = URL.createObjectURL(file);
                    previewVideo.src = videoURL;
                    updateVideoView();

                    // Show file info
                    const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
                    showSideNotification(`Video selected: ${file.name} (${fileSizeMB}MB)`);
                    console.log('Video selected for preview:', file.name, 'Size:', fileSizeMB + 'MB');
                } else {
                    showSideNotification('Please select a valid video file.');
                }
            });

            // Add drag and drop functionality to the video placeholder
            const videoPlaceholder = document.getElementById('videoPlaceholder');

            if (videoPlaceholder) {
                // Prevent default drag behaviors
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    videoPlaceholder.addEventListener(eventName, preventDefaults, false);
                });

                // Highlight drop area when item is dragged over it
                ['dragenter', 'dragover'].forEach(eventName => {
                    videoPlaceholder.addEventListener(eventName, highlight, false);
                });

                ['dragleave', 'drop'].forEach(eventName => {
                    videoPlaceholder.addEventListener(eventName, unhighlight, false);
                });

                // Handle dropped files
                videoPlaceholder.addEventListener('drop', handleDrop, false);

                function preventDefaults(e) {
                    e.preventDefault();
                    e.stopPropagation();
                }

                function highlight(e) {
                    videoPlaceholder.style.borderColor = '#007bff';
                    videoPlaceholder.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
                }

                function unhighlight(e) {
                    videoPlaceholder.style.borderColor = '#dee2e6';
                    videoPlaceholder.style.backgroundColor = '#f8f9fa';
                }

                function handleDrop(e) {
                    const dt = e.dataTransfer;
                    const files = dt.files;

                    if (files.length > 0) {
                        const file = files[0];
                        if (file && file.type.startsWith('video/')) {
                            // Check file size (50MB max)
                            const maxSize = 50 * 1024 * 1024; // 50MB
                            if (file.size > maxSize) {
                                showSideNotification('Video file size must be less than 50MB. Current size: ' + (file.size / (1024 * 1024)).toFixed(2) + 'MB');
                                return;
                            }

                            // Validate video format
                            const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm', 'video/mkv'];
                            if (!allowedTypes.includes(file.type)) {
                                showSideNotification('Please select a supported video format: MP4, AVI, MOV, WMV, WebM, or MKV');
                                return;
                            }

                            selectedVideoFile = file;

                            // Show preview
                            const videoURL = URL.createObjectURL(file);
                            previewVideo.src = videoURL;
                            updateVideoView();

                            // Show file info
                            const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
                            showSideNotification(`Video dropped and selected: ${file.name} (${fileSizeMB}MB)`);
                            console.log('Video dropped and selected:', file.name, 'Size:', fileSizeMB + 'MB');
                        } else {
                            showSideNotification('Please drop a valid video file.');
                        }
                    }
                }
            }
        }

        // Save video functionality
        if (saveVideoBtn) {
            saveVideoBtn.addEventListener('click', async () => {
                if (!selectedVideoFile) {
                    showSideNotification('No video selected to save');
                    return;
                }

                // Double-check file size before upload
                const maxSize = 50 * 1024 * 1024; // 50MB
                if (selectedVideoFile.size > maxSize) {
                    showSideNotification('Video file is too large. Please select a file smaller than 50MB.');
                    return;
                }

                // Show loading state
                saveVideoBtn.disabled = true;
                saveVideoBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

                try {
                    const formData = new FormData();
                    formData.append('video', selectedVideoFile);

                    const response = await fetch('/upload_profile_video', {
                        method: 'POST',
                        body: formData
                    });

                    const data = await response.json();
                    if (data.success) {
                        console.log('✅ Video uploaded successfully');
                        showSideNotification('Profile video saved successfully!');

                        // Move from preview to saved state
                        hasVideo = true;
                        video.src = `/api/profile-video/genius/{{ genius.id }}?t=${new Date().getTime()}`;
                        selectedVideoFile = null;
                        updateVideoView();
                    } else {
                        console.error('❌ Video upload failed:', data.error);
                        alert('Error saving video: ' + data.error);
                    }
                } catch (error) {
                    console.error('❌ Video upload error:', error);
                    showSideNotification('An error occurred while saving the video: ' + error.message);
                } finally {
                    // Reset button state
                    saveVideoBtn.disabled = false;
                    saveVideoBtn.innerHTML = '<i class="fas fa-save"></i> Save Video';
                }
            });
        }

        // Cancel video functionality
        if (cancelVideoBtn) {
            cancelVideoBtn.addEventListener('click', () => {
                selectedVideoFile = null;
                previewVideo.src = '';
                updateVideoView();
                console.log('Video selection cancelled');
            });
        }

        // Replace video functionality
        if (replaceVideoBtn) {
            replaceVideoBtn.addEventListener('click', () => {
                videoUpload.click();
            });
        }

        // Delete video functionality
        if (deleteVideoBtn) {
            deleteVideoBtn.addEventListener('click', async () => {
                if (confirm('Are you sure you want to delete your profile video?')) {
                    try {
                        const response = await fetch('/delete_profile_video', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        const data = await response.json();
                        if (data.success) {
                            video.src = '';
                            hasVideo = false;
                            updateVideoView();
                            console.log('✅ Video deleted successfully');
                            showSideNotification('Profile video deleted successfully!');
                        } else {
                            console.error('❌ Video deletion failed:', data.error);
                            showSideNotification('Error deleting video: ' + data.error);
                        }
                    } catch (error) {
                        console.error('❌ Video deletion error:', error);
                        showSideNotification('An error occurred while deleting the video: ' + error.message);
                    }
                }
            });
        }

        // Video expand/collapse functionality
        if (expandBtn) {
            expandBtn.addEventListener('click', () => {
                videoContainer.classList.toggle('expanded');
                if (videoContainer.classList.contains('expanded')) {
                    expandBtn.innerHTML = '<i class="fas fa-compress"></i> Exit Fullscreen';
                    document.body.style.overflow = 'hidden';
                } else {
                    expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                    document.body.style.overflow = '';
                }
            });
        }

        // Close expanded video when clicking outside
        document.addEventListener('click', (event) => {
            if (videoContainer && videoContainer.classList.contains('expanded') &&
                !videoPlayer.contains(event.target) &&
                !expandBtn.contains(event.target)) {
                videoContainer.classList.remove('expanded');
                if (expandBtn) {
                    expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                }
                document.body.style.overflow = '';
            }
        });

        // Close expanded video when pressing Escape key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && videoContainer && videoContainer.classList.contains('expanded')) {
                videoContainer.classList.remove('expanded');
                if (expandBtn) {
                    expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                }
                document.body.style.overflow = '';
            }
        });

        // This section has been moved to the DOMContentLoaded event below to avoid conflicts

        // Mobile Menu Toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.getElementById('mobileMenu');

        if (mobileMenuBtn && mobileMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('active');
                if (mobileMenu.classList.contains('active')) {
                    mobileMenuBtn.innerHTML = '<i class="fas fa-times"></i>';
                } else {
                    mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
                }
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (event) => {
                if (!mobileMenuBtn.contains(event.target) && !mobileMenu.contains(event.target)) {
                    mobileMenu.classList.remove('active');
                    mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded - Initializing modals...');

            // Debug: Log the genius data
            console.log('Genius country value:', '{{ genius.country }}');
            console.log('Genius data:', {
                country: '{{ genius.country }}',
                availability: '{{ genius.availability }}',
                expertise: '{{ genius.expertise }}',
                position: '{{ genius.position }}'
            });

            // Modal functionality
            const professionalSummaryModal = document.getElementById('professionalSummaryModal');
            const introductionModal = document.getElementById('introductionModal');
            const editProfileModal = document.getElementById('editProfileModal');
            const portfolioViewModal = document.getElementById('portfolioViewModal');
            const portfolioAddModal = document.getElementById('portfolioAddModal');
            const portfolioReorderModal = document.getElementById('portfolioReorderModal');
            const certificationEditModal = document.getElementById('certificationEditModal');

            // Edit buttons
            const editSummary = document.getElementById('editSummaryBtn');
            const editIntroduction = document.getElementById('editIntroductionBtn');
            const editProfile = document.getElementById('editProfileBtn');
            const portfolioAdd = document.getElementById('portfolioAddBtn');
            const portfolioRefresh = document.querySelector('.portfolio-refresh-btn');
            const portfolioViewCloseBtn = document.getElementById('portfolioViewCloseBtn');
            const portfolioAddCloseBtn = document.getElementById('portfolioAddCloseBtn');
            const portfolioReorderCloseBtn = document.getElementById('portfolioReorderCloseBtn');
            const certificationEdit1 = document.getElementById('certificationEditBtn1');
            const certificationEdit2 = document.getElementById('certificationEditBtn2');

            // Back buttons
            const summaryBackBtn = document.getElementById('summaryBackBtn');
            const introBackBtn = document.getElementById('introBackBtn');
            const profileBackBtn = document.getElementById('profileBackBtn');
            const portfolioAddBackBtn = document.getElementById('portfolioAddBackBtn');
            const portfolioReorderCancelBtn = document.getElementById('portfolioReorderCancelBtn');
            const certificationBackBtn = document.getElementById('certificationBackBtn');

            console.log('Modal elements found:', {
                professionalSummaryModal: !!professionalSummaryModal,
                editSummary: !!editSummary,
                editIntroduction: !!editIntroduction,
                editProfile: !!editProfile
            });

            // Close modals function
            function closeModal(modal) {
                if (modal) {
                    modal.classList.remove('active');
                    document.body.style.overflow = '';
                }
            }

            // Open modals - with null checks and debugging
            if (editSummary && professionalSummaryModal) {
                editSummary.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Edit Summary clicked');

                    // Load current text into textarea when opening modal
                    const summaryText = document.getElementById('summaryText');
                    const professionalSummaryTextarea = document.getElementById('professionalSummaryTextarea');

                    if (summaryText && professionalSummaryTextarea) {
                        // Get the current text, excluding the placeholder text
                        const currentText = summaryText.textContent || summaryText.innerText || '';
                        const placeholderText = 'Add a professional summary to showcase your skills and experience to potential clients. Click the Edit button to get started.';

                        if (currentText.trim() === placeholderText.trim()) {
                            professionalSummaryTextarea.value = '';
                        } else {
                            professionalSummaryTextarea.value = currentText.trim();
                        }
                    }

                    professionalSummaryModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (editIntroduction && introductionModal) {
                editIntroduction.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Edit Introduction clicked');
                    introductionModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (editProfile && editProfileModal) {
                editProfile.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Edit Profile clicked');

                    try {
                        // Reset profile photo to show current photo when modal opens
                        const currentProfilePhoto = document.getElementById('currentProfilePhoto');
                        const navProfilePhoto = document.getElementById('navProfilePhoto');
                        if (currentProfilePhoto) {
                            // Force reload the current photo with timestamp to avoid cache
                            const timestamp = new Date().getTime();
                            currentProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                            // Also update navigation photo to ensure consistency
                            if (navProfilePhoto) {
                                navProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                            }
                        }

                        // Fetch current profile data from database
                        const response = await fetch('/api/genius/profile_data');
                        const data = await response.json();

                        if (data.success && data.profile_data) {
                            const profileData = data.profile_data;

                            // Set form values with database data
                            const emailField = document.getElementById('email');
                            const mobileField = document.getElementById('mobile');
                            const positionField = document.getElementById('position');
                            const expertiseField = document.getElementById('expertise');
                            const rateField = document.getElementById('rate');
                            const availabilityField = document.getElementById('availability');
                            const countryField = document.getElementById('country');
                            const languageField = document.getElementById('language');

                            if (emailField) emailField.value = profileData.email || '';
                            if (mobileField) mobileField.value = profileData.mobile || '';
                            if (positionField) positionField.value = profileData.position || '';
                            if (expertiseField) expertiseField.value = profileData.expertise || 'Beginner';
                            if (rateField) rateField.value = profileData.hourly_rate || '';
                            if (availabilityField) availabilityField.value = profileData.availability || 'fulltime';
                            if (countryField) countryField.value = profileData.country || '';
                            if (languageField) languageField.value = profileData.language || 'English';
                        } else {
                            console.error('Failed to fetch profile data:', data.error);
                        }
                    } catch (error) {
                        console.error('Error fetching profile data:', error);
                    }

                    editProfileModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (portfolioAdd && portfolioAddModal) {
                portfolioAdd.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Portfolio Add clicked');
                    portfolioAddModal.classList.add('active');
                    document.body.style.overflow = 'hidden';

                    // Setup portfolio content button listeners when modal opens
                    setTimeout(() => {
                        setupPortfolioButtons();
                    }, 100);
                });
            }

            if (portfolioRefresh && portfolioReorderModal) {
                portfolioRefresh.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Portfolio Refresh clicked');
                    portfolioReorderModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (certificationEdit1 && certificationEditModal) {
                certificationEdit1.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Certification Edit 1 clicked');
                    certificationEditModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (certificationEdit2 && certificationEditModal) {
                certificationEdit2.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Certification Edit 2 clicked');
                    certificationEditModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            // Close modals with back buttons
            if (summaryBackBtn) {
                summaryBackBtn.addEventListener('click', () => {
                    console.log('Summary back button clicked');
                    closeModal(professionalSummaryModal);
                });
            }

            if (introBackBtn) {
                introBackBtn.addEventListener('click', () => {
                    console.log('Intro back button clicked');
                    closeModal(introductionModal);
                });
            }

            if (profileBackBtn) {
                profileBackBtn.addEventListener('click', () => {
                    console.log('Profile back button clicked');
                    closeModal(editProfileModal);
                });
            }

            if (portfolioViewCloseBtn) {
                portfolioViewCloseBtn.addEventListener('click', () => {
                    console.log('Portfolio view close button clicked');
                    closeModal(portfolioViewModal);
                });
            }

            if (portfolioAddCloseBtn) {
                portfolioAddCloseBtn.addEventListener('click', () => {
                    console.log('Portfolio add close button clicked');
                    closeModal(portfolioAddModal);
                });
            }

            if (portfolioAddBackBtn) {
                portfolioAddBackBtn.addEventListener('click', () => {
                    console.log('Portfolio add back button clicked');
                    closeModal(portfolioAddModal);
                });
            }

            if (portfolioReorderCloseBtn) {
                portfolioReorderCloseBtn.addEventListener('click', () => {
                    console.log('Portfolio reorder close button clicked');
                    closeModal(portfolioReorderModal);
                });
            }

            if (portfolioReorderCancelBtn) {
                portfolioReorderCancelBtn.addEventListener('click', () => {
                    console.log('Portfolio reorder cancel button clicked');
                    closeModal(portfolioReorderModal);
                });
            }

            if (certificationBackBtn) {
                certificationBackBtn.addEventListener('click', () => {
                    console.log('Certification back button clicked');
                    closeModal(certificationEditModal);
                });
            }

            // Close modals when clicking outside
            window.addEventListener('click', (event) => {
                if (event.target === professionalSummaryModal) {
                    closeModal(professionalSummaryModal);
                }
                if (event.target === introductionModal) {
                    closeModal(introductionModal);
                }
                if (event.target === editProfileModal) {
                    closeModal(editProfileModal);
                }
                if (event.target === portfolioViewModal) {
                    closeModal(portfolioViewModal);
                }
                if (event.target === portfolioAddModal) {
                    closeModal(portfolioAddModal);
                }
                if (event.target === portfolioReorderModal) {
                    closeModal(portfolioReorderModal);
                }
                if (event.target === certificationEditModal) {
                    closeModal(certificationEditModal);
                }
            });

            // Close modals with Escape key
            document.addEventListener('keydown', (event) => {
                if (event.key === 'Escape') {
                    closeModal(professionalSummaryModal);
                    closeModal(introductionModal);
                    closeModal(editProfileModal);
                    closeModal(portfolioViewModal);
                    closeModal(portfolioAddModal);
                    closeModal(portfolioReorderModal);
                    closeModal(certificationEditModal);
                }
            });

            // For mobile devices, make dropdown items clickable to expand
            const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');

            dropdownTriggers.forEach(trigger => {
                trigger.addEventListener('click', function(e) {
                    if (window.innerWidth < 768) {
                        e.preventDefault();
                        const dropdown = this.nextElementSibling;
                        dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
                    }
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    const dropdownMenus = document.querySelectorAll('.dropdown-menu');
                    dropdownMenus.forEach(menu => {
                        if (window.innerWidth < 768) {
                            menu.style.display = 'none';
                        }
                    });
                }

                // Close profile dropdown when clicking outside
                if (!e.target.closest('.profile-dropdown')) {
                    const profileDropdown = document.getElementById('profileDropdown');
                    if (profileDropdown) {
                        profileDropdown.classList.remove('active');
                    }
                }

                // Close notification dropdown when clicking outside
                if (!e.target.closest('.notification-dropdown')) {
                    const notificationDropdown = document.getElementById('notificationDropdown');
                    if (notificationDropdown) {
                        notificationDropdown.classList.remove('active');
                    }
                }
            });

            // Profile dropdown toggle
            const profileIcon = document.getElementById('profileIcon');
            const profileDropdown = document.getElementById('profileDropdown');

            if (profileIcon && profileDropdown) {
                profileIcon.addEventListener('click', function(e) {
                    e.stopPropagation();
                    profileDropdown.classList.toggle('active');

                    // Close notification dropdown if open
                    const notificationDropdown = document.getElementById('notificationDropdown');
                    if (notificationDropdown && notificationDropdown.classList.contains('active')) {
                        notificationDropdown.classList.remove('active');
                    }
                });

                profileDropdown.addEventListener('click', (event) => {
                    event.stopPropagation();
                });
            }

            // Notification dropdown toggle
            const notificationBtn = document.getElementById('notificationBtn');
            const notificationDropdown = document.getElementById('notificationDropdown');
            const markAllReadBtn = document.getElementById('markAllRead');

            if (notificationBtn && notificationDropdown) {
                notificationBtn.addEventListener('click', function(event) {
                    event.stopPropagation();
                    notificationDropdown.classList.toggle('active');

                    // Close profile dropdown if open
                    if (profileDropdown && profileDropdown.classList.contains('active')) {
                        profileDropdown.classList.remove('active');
                    }
                });

                notificationDropdown.addEventListener('click', (event) => {
                    event.stopPropagation();
                });

                // Mark all notifications as read
                if (markAllReadBtn) {
                    markAllReadBtn.addEventListener('click', function() {
                        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
                        unreadNotifications.forEach(notification => {
                            notification.classList.remove('unread');
                        });

                        // Remove the notification indicator
                        const indicator = document.querySelector('.notification-indicator');
                        if (indicator) {
                            indicator.style.display = 'none';
                        }
                    });
                }

                // Mark individual notification as read when clicked
                const notificationItems = document.querySelectorAll('.notification-item');
                notificationItems.forEach(item => {
                    item.addEventListener('click', function() {
                        this.classList.remove('unread');

                        // Check if there are any unread notifications left
                        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
                        if (unreadNotifications.length === 0) {
                            const indicator = document.querySelector('.notification-indicator');
                            if (indicator) {
                                indicator.style.display = 'none';
                            }
                        }
                    });
                });
            }

            // Professional Summary Next Button functionality
            const summaryNextBtn = document.getElementById('summaryNextBtn');
            const professionalSummaryTextarea = document.getElementById('professionalSummaryTextarea');

            if (summaryNextBtn && professionalSummaryTextarea) {
                summaryNextBtn.addEventListener('click', async function(e) {
                    e.preventDefault();

                    const professionalSummary = professionalSummaryTextarea.value.trim();

                    if (!professionalSummary) {
                        alert('Please enter a professional summary before saving.');
                        return;
                    }

                    // Show loading state
                    summaryNextBtn.disabled = true;
                    summaryNextBtn.textContent = 'Saving...';

                    try {
                        const response = await fetch('/update_professional_summary', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                professional_summary: professionalSummary
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            // Update the display text
                            const summaryText = document.getElementById('summaryText');
                            if (summaryText) {
                                if (professionalSummary.trim()) {
                                    summaryText.innerHTML = professionalSummary;
                                } else {
                                    summaryText.innerHTML = '<span style="color: #6b7280; font-style: italic;">Add a professional summary to showcase your skills and experience to potential clients. Click the Edit button to get started.</span>';
                                }
                            }

                            // Close the modal
                            closeModal(professionalSummaryModal);

                            // Show side notification instead of alert
                            showSideNotification('Professional summary has been updated');
                        } else {
                            alert('Error: ' + (data.error || 'Failed to update professional summary'));
                        }
                    } catch (error) {
                        console.error('Error updating professional summary:', error);
                        alert('An error occurred while updating your professional summary. Please try again.');
                    } finally {
                        // Reset button state
                        summaryNextBtn.disabled = false;
                        summaryNextBtn.textContent = 'Next';
                    }
                });
            }

            // Introduction Next Button functionality
            const introNextBtn = document.getElementById('introNextBtn');
            const introductionTextarea = document.getElementById('introductionTextarea');

            if (introNextBtn && introductionTextarea) {
                introNextBtn.addEventListener('click', async function(e) {
                    e.preventDefault();

                    const introduction = introductionTextarea.value.trim();
                    console.log('Introduction content:', introduction);

                    if (!introduction) {
                        alert('Please enter an introduction before saving.');
                        return;
                    }

                    // Show loading state
                    introNextBtn.disabled = true;
                    introNextBtn.textContent = 'Saving...';

                    try {
                        console.log('Sending request to /introduction');
                        const response = await fetch('/introduction', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                introduction: introduction
                            })
                        });

                        console.log('Response status:', response.status);
                        const data = await response.json();
                        console.log('Response data:', data);

                        if (data.success) {
                            // Update the display text
                            const introductionDisplay = document.getElementById('introductionDisplay');
                            if (introductionDisplay) {
                                introductionDisplay.value = introduction;
                            }

                            // Close the modal
                            closeModal(introductionModal);

                            // Show side notification
                            showSideNotification('Introduction updated successfully!');
                        } else {
                            alert('Error: ' + (data.error || 'Failed to update introduction'));
                        }
                    } catch (error) {
                        console.error('Error updating introduction:', error);
                        alert('An error occurred while updating your introduction. Please try again.');
                    } finally {
                        // Reset button state
                        introNextBtn.disabled = false;
                        introNextBtn.textContent = 'Next';
                    }
                });
            }
            // Profile photo upload functionality
            const profileUpload = document.getElementById('profile-upload');
            const profilePhoto = document.querySelector('.profile-photo img');
            let selectedFile = null;
            let originalPhotoSrc = null; // Store original photo URL

            if (profileUpload) {
                // Store the original photo source when page loads
                originalPhotoSrc = profilePhoto.src;

                profileUpload.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        // Validate file type
                        if (!file.type.startsWith('image/')) {
                            alert('Please select an image file (JPEG, PNG, etc.)');
                            e.target.value = '';
                            return;
                        }

                        // Validate file size (2MB limit)
                        if (file.size > 2 * 1024 * 1024) {
                            alert('File size must be less than 2MB');
                            e.target.value = '';
                            return;
                        }

                        selectedFile = file;

                        // Preview the new image
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            profilePhoto.src = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    } else {
                        // If no file selected, revert to original photo
                        selectedFile = null;
                        profilePhoto.src = originalPhotoSrc;
                    }
                });
            }

            // Profile save functionality
            const saveProfileBtn = document.getElementById('saveProfileBtn');
            if (saveProfileBtn) {
                saveProfileBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Edit Profile clicked - saving profile data');

                    // Get form values
                    const email = document.getElementById('email').value.trim();
                    const mobile = document.getElementById('mobile').value.trim();
                    const position = document.getElementById('position').value.trim();
                    const expertise = document.getElementById('expertise').value;
                    const hourly_rate = document.getElementById('rate').value;
                    const availability = document.getElementById('availability').value;
                    const country = document.getElementById('country').value;
                    const language = document.getElementById('language').value;

                    // Validate required fields
                    if (!email || !mobile || !position || !expertise || !hourly_rate || !availability || !country) {
                        alert('Please fill in all required fields');
                        return;
                    }

                    // Validate email format
                    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                    if (!emailPattern.test(email)) {
                        alert('Please enter a valid email address');
                        return;
                    }

                    // Validate hourly rate
                    if (isNaN(hourly_rate) || parseFloat(hourly_rate) < 0) {
                        alert('Please enter a valid hourly rate');
                        return;
                    }

                    try {
                        // Show loading state
                        saveProfileBtn.disabled = true;
                        saveProfileBtn.textContent = 'Saving...';

                        // Check if we have a profile photo to upload
                        if (selectedFile) {
                            // Use FormData for file upload
                            const formData = new FormData();
                            formData.append('email', email);
                            formData.append('mobile', mobile);
                            formData.append('position', position);
                            formData.append('expertise', expertise);
                            formData.append('hourly_rate', parseFloat(hourly_rate));
                            formData.append('availability', availability);
                            formData.append('country', country);
                            formData.append('language', language);
                            formData.append('profile_photo', selectedFile);

                            const response = await fetch('/update_genius_profile', {
                                method: 'POST',
                                body: formData
                            });

                            const data = await response.json();

                            if (data.success) {
                                // Update all display elements immediately
                                const displayPosition = document.getElementById('displayPosition');
                                const displayCountry = document.getElementById('displayCountry');
                                const profilePosition = document.getElementById('profilePosition');
                                const displayAvailability = document.getElementById('displayAvailability');
                                const displayLanguage = document.getElementById('displayLanguage');
                                const displayCountryField = document.getElementById('displayCountryField');
                                const hourlyRateElement = document.getElementById('hourlyRate');

                                if (displayPosition) displayPosition.textContent = position;
                                if (displayCountry) displayCountry.textContent = country;
                                if (profilePosition) profilePosition.textContent = position;
                                if (displayAvailability) {
                                    const displayText = availability === 'fulltime' ? 'Full-Time' : availability === 'parttime' ? 'Part-Time' : availability;
                                    displayAvailability.value = displayText;
                                }
                                if (displayLanguage) displayLanguage.value = language;
                                if (displayCountryField) displayCountryField.value = country;
                                if (hourlyRateElement) {
                                    if (hourly_rate && hourly_rate > 0) {
                                        hourlyRateElement.textContent = `$${hourly_rate}`;
                                    } else {
                                        hourlyRateElement.textContent = 'No rate set';
                                    }
                                }

                                // Update navigation profile photo if a new photo was uploaded
                                if (selectedFile) {
                                    const timestamp = new Date().getTime();
                                    const navProfilePhoto = document.getElementById('navProfilePhoto');
                                    if (navProfilePhoto) {
                                        navProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                                    }
                                    // Also update the modal photo to show the saved version
                                    const currentProfilePhoto = document.getElementById('currentProfilePhoto');
                                    if (currentProfilePhoto) {
                                        currentProfilePhoto.src = `/api/profile-photo/genius/{{ genius.id }}?t=${timestamp}`;
                                    }
                                }

                                showSideNotification('Profile updated successfully!');
                                closeModal(editProfileModal);
                                // Don't reload the page, just update the display
                            } else {
                                alert('Error: ' + (data.error || 'Failed to update profile'));
                            }
                        } else {
                            // Use JSON for regular data without file upload
                            const response = await fetch('/update_genius_profile', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    email: email,
                                    mobile: mobile,
                                    position: position,
                                    expertise: expertise,
                                    hourly_rate: parseFloat(hourly_rate),
                                    availability: availability,
                                    country: country,
                                    language: language
                                })
                            });

                            const data = await response.json();

                            if (data.success) {
                                // Update all display elements immediately
                                const displayPosition = document.getElementById('displayPosition');
                                const displayCountry = document.getElementById('displayCountry');
                                const profilePosition = document.getElementById('profilePosition');
                                const displayAvailability = document.getElementById('displayAvailability');
                                const displayLanguage = document.getElementById('displayLanguage');
                                const displayCountryField = document.getElementById('displayCountryField');
                                const hourlyRateElement = document.getElementById('hourlyRate');

                                if (displayPosition) displayPosition.textContent = position;
                                if (displayCountry) displayCountry.textContent = country;
                                if (profilePosition) profilePosition.textContent = position;
                                if (displayAvailability) {
                                    const displayText = availability === 'fulltime' ? 'Full-Time' : availability === 'parttime' ? 'Part-Time' : availability;
                                    displayAvailability.value = displayText;
                                }
                                if (displayLanguage) displayLanguage.value = language;
                                if (displayCountryField) displayCountryField.value = country;
                                if (hourlyRateElement) {
                                    if (hourly_rate && hourly_rate > 0) {
                                        hourlyRateElement.textContent = `$${hourly_rate}`;
                                    } else {
                                        hourlyRateElement.textContent = 'No rate set';
                                    }
                                }

                                showSideNotification('Profile updated successfully!');
                                closeModal(editProfileModal);
                                // Don't reload the page, just update the display
                            } else {
                                alert('Error: ' + (data.error || 'Failed to update profile'));
                            }
                        }
                    } catch (error) {
                        alert('An error occurred while saving your profile');
                        console.error('Profile update error:', error);
                    } finally {
                        saveProfileBtn.disabled = false;
                        saveProfileBtn.textContent = 'Next';
                    }
                });
            }



            // Portfolio save functionality
            const portfolioAddSaveBtn = document.getElementById('portfolioAddSaveBtn');
            console.log('Portfolio save button found:', portfolioAddSaveBtn);
            if (portfolioAddSaveBtn) {
                portfolioAddSaveBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Portfolio Next: Preview clicked');

                    // Get form values
                    const projectTitle = document.getElementById('projectTitle').value.trim();
                    const projectRole = document.getElementById('projectRole').value.trim();
                    const projectDescription = document.getElementById('portfolioContent').value.trim();
                    const projectContent = document.getElementById('portfolioProjectContent').innerHTML.trim();
                    const projectSkills = document.getElementById('projectSkills').value.trim();
                    const relatedJob = document.getElementById('relatedJob').value.trim();

                    // Validate required fields
                    if (!projectTitle) {
                        alert('Please enter a project title');
                        return;
                    }

                    if (!projectDescription) {
                        alert('Please enter a project description');
                        return;
                    }

                    try {
                        // Show loading state
                        portfolioAddSaveBtn.disabled = true;
                        portfolioAddSaveBtn.textContent = 'Saving...';

                        console.log('Saving project and publishing - Title:', projectTitle, 'Role:', projectRole, 'Description:', projectDescription);

                        // Send request to save portfolio title as published
                        const response = await fetch('/save_portfolio_title', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                project_title: projectTitle,
                                project_role: projectRole,
                                project_description: projectDescription,
                                project_content: projectContent,
                                skills_and_deliverables: projectSkills,
                                related_giggenius_job: relatedJob,
                                action: 'published'
                            })
                        });

                        console.log('Response status:', response.status);
                        const data = await response.json();
                        console.log('Response data:', data);

                        if (data.success) {
                            // Show success notification with portfolio icon
                            showPortfolioNotification('Project published successfully! Your portfolio has been updated.', 'success', 'fas fa-rocket');

                            // Reset form
                            document.getElementById('projectTitle').value = '';
                            document.getElementById('projectRole').value = '';
                            document.getElementById('projectSkills').value = '';
                            document.getElementById('relatedJob').value = '';
                            document.getElementById('portfolioContent').value = '';
                            document.getElementById('portfolioProjectContent').innerHTML = '';

                            closeModal(portfolioAddModal);

                            // Navigate to Published section and reload page after notification shows
                            setTimeout(() => {
                                if (data.redirect === 'published') {
                                    // Switch to published tab
                                    const publishedTab = document.querySelector('[data-tab="published"]');
                                    if (publishedTab) {
                                        publishedTab.click();
                                    }
                                }

                                // Reload the page to show updated project title
                                window.location.reload();
                            }, 1500);
                        } else {
                            showPortfolioNotification('Error: ' + (data.error || 'Failed to save and publish project title'), 'error', 'fas fa-exclamation-triangle');
                        }
                    } catch (error) {
                        showPortfolioNotification('An error occurred while saving your project. Please try again.', 'error', 'fas fa-exclamation-circle');
                        console.error('Portfolio save error:', error);
                    } finally {
                        portfolioAddSaveBtn.disabled = false;
                        portfolioAddSaveBtn.textContent = 'Next: Preview';
                    }
                });
            }



            // Portfolio Tabs functionality
            const portfolioTabs = document.querySelectorAll('.portfolio-tab');
            const publishedContent = document.getElementById('publishedContent');
            const draftsContent = document.getElementById('draftsContent');

            portfolioTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs
                    portfolioTabs.forEach(t => t.classList.remove('active'));
                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Show/hide different portfolio content
                    const tabType = this.getAttribute('data-tab');
                    console.log('Switched to tab:', tabType);

                    if (tabType === 'published') {
                        publishedContent.style.display = 'block';
                        draftsContent.style.display = 'none';
                    } else if (tabType === 'drafts') {
                        publishedContent.style.display = 'none';
                        draftsContent.style.display = 'block';
                    }
                });
            });

            // Portfolio Pagination functionality
            const paginationBtns = document.querySelectorAll('.pagination-btn');
            paginationBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    if (!this.classList.contains('prev') && !this.classList.contains('next')) {
                        // Remove active class from all pagination buttons
                        paginationBtns.forEach(b => b.classList.remove('active'));
                        // Add active class to clicked button
                        this.classList.add('active');
                    }
                });
            });

            // Portfolio Draft Save functionality
            if (portfolioAddBackBtn) {
                portfolioAddBackBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Portfolio Save as Draft clicked');

                    // Get form values
                    const projectTitle = document.getElementById('projectTitle').value.trim();
                    const projectRole = document.getElementById('projectRole').value.trim();
                    const projectDescription = document.getElementById('portfolioContent').value.trim();
                    const projectContent = document.getElementById('portfolioProjectContent').innerHTML.trim();
                    const projectSkills = document.getElementById('projectSkills').value.trim();
                    const relatedJob = document.getElementById('relatedJob').value.trim();

                    // Validate required fields (only title required for draft)
                    if (!projectTitle) {
                        alert('Please enter a project title to save as draft');
                        return;
                    }

                    try {
                        // Show loading state
                        portfolioAddBackBtn.disabled = true;
                        portfolioAddBackBtn.textContent = 'Saving...';

                        console.log('Saving project as draft - Title:', projectTitle, 'Role:', projectRole, 'Description:', projectDescription);

                        // Send request to save portfolio title
                        const response = await fetch('/save_portfolio_title', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                project_title: projectTitle,
                                project_role: projectRole,
                                project_description: projectDescription,
                                project_content: projectContent,
                                skills_and_deliverables: projectSkills,
                                related_giggenius_job: relatedJob,
                                action: 'draft'
                            })
                        });

                        console.log('Draft response status:', response.status);
                        const data = await response.json();
                        console.log('Draft response data:', data);

                        if (data.success) {
                            // Show success notification with draft icon
                            showPortfolioNotification('Project saved as draft successfully! You can publish it later.', 'success', 'fas fa-save');

                            // Reset form
                            document.getElementById('projectTitle').value = '';
                            document.getElementById('projectRole').value = '';
                            document.getElementById('projectSkills').value = '';
                            document.getElementById('relatedJob').value = '';
                            document.getElementById('portfolioContent').value = '';
                            document.getElementById('portfolioProjectContent').innerHTML = '';

                            closeModal(portfolioAddModal);

                            // Navigate to Drafts section and reload page after notification shows
                            setTimeout(() => {
                                if (data.redirect === 'drafts') {
                                    // Switch to drafts tab
                                    const draftsTab = document.querySelector('[data-tab="drafts"]');
                                    if (draftsTab) {
                                        draftsTab.click();
                                    }
                                }

                                // Reload the page to show updated project title
                                window.location.reload();
                            }, 1500);
                        } else {
                            showPortfolioNotification('Error: ' + (data.error || 'Failed to save project title as draft'), 'error', 'fas fa-exclamation-triangle');
                        }
                    } catch (error) {
                        showPortfolioNotification('An error occurred while saving your project as draft. Please try again.', 'error', 'fas fa-exclamation-circle');
                        console.error('Portfolio draft save error:', error);
                    } finally {
                        portfolioAddBackBtn.disabled = false;
                        portfolioAddBackBtn.textContent = 'Save as draft';
                    }
                });
            }

            // Portfolio View/Publish functionality
            const publishPortfolioBtns = document.querySelectorAll('.publish-portfolio-btn');
            publishPortfolioBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const portfolioCard = this.closest('.portfolio-card');
                    const projectTitle = portfolioCard.getAttribute('data-project-title');
                    const projectRole = portfolioCard.getAttribute('data-project-role');
                    const projectDescription = portfolioCard.getAttribute('data-project-description');
                    const projectSkills = portfolioCard.getAttribute('data-project-skills');
                    const projectImage = portfolioCard.getAttribute('data-project-image');

                    // Update modal content
                    const modalTitle = portfolioViewModal.querySelector('.project-title');
                    const modalRole = portfolioViewModal.querySelector('.project-role strong');
                    const modalDescription = portfolioViewModal.querySelector('.project-description');
                    const modalImage = portfolioViewModal.querySelector('#portfolioViewImage');
                    const modalSkillsContainer = portfolioViewModal.querySelector('.skills-tags');

                    if (modalTitle) modalTitle.textContent = projectTitle;
                    if (modalRole) modalRole.textContent = projectRole;
                    if (modalDescription) modalDescription.textContent = projectDescription;
                    if (modalImage) modalImage.src = projectImage;

                    // Update skills
                    if (modalSkillsContainer && projectSkills) {
                        modalSkillsContainer.innerHTML = '';
                        const skills = projectSkills.split(',');
                        skills.forEach(skill => {
                            const skillTag = document.createElement('span');
                            skillTag.className = 'skill-tag';
                            skillTag.style.cssText = 'background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;';
                            skillTag.textContent = skill.trim();
                            modalSkillsContainer.appendChild(skillTag);
                        });
                    }

                    // Open modal
                    portfolioViewModal.classList.add('active');
                    document.body.style.overflow = 'hidden';

                    console.log('Portfolio view opened for:', projectTitle);
                });
            });

            // Portfolio Card Click functionality for modal view
            const clickableCards = document.querySelectorAll('.clickable-card');
            clickableCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    e.preventDefault();

                    const projectId = this.getAttribute('data-project-id');
                    const projectTitle = this.getAttribute('data-project-title');
                    const projectRole = this.getAttribute('data-project-role');
                    const projectDescription = this.getAttribute('data-project-description');
                    const projectContent = this.getAttribute('data-project-content');
                    const projectSkills = this.getAttribute('data-project-skills');
                    const relatedJob = this.getAttribute('data-related-job');
                    const hasImage = this.getAttribute('data-has-image') === 'true';
                    const isPublished = !this.getAttribute('data-status'); // draft has data-status="draft"

                    console.log('Project clicked:', {
                        id: projectId,
                        title: projectTitle,
                        role: projectRole,
                        description: projectDescription,
                        content: projectContent,
                        skills: projectSkills,
                        relatedJob: relatedJob
                    });

                    // Update modal content
                    const modalTitle = portfolioViewModal.querySelector('.project-title');
                    const modalRole = portfolioViewModal.querySelector('.project-role strong');
                    const modalDescription = portfolioViewModal.querySelector('.project-description');
                    const modalImage = portfolioViewModal.querySelector('#portfolioViewImage');
                    const modalSkillsContainer = portfolioViewModal.querySelector('.skills-tags');
                    const contentSection = portfolioViewModal.querySelector('.content-section');
                    const projectContentDiv = portfolioViewModal.querySelector('.project-content');
                    const relatedJobSection = portfolioViewModal.querySelector('.related-job-section');
                    const relatedJobText = portfolioViewModal.querySelector('.related-job-text');

                    if (modalTitle) modalTitle.textContent = projectTitle;
                    if (modalRole) modalRole.textContent = projectRole || 'Not specified';

                    // Handle project description - extract text from HTML if needed
                    if (modalDescription) {
                        if (projectDescription) {
                            // If description contains HTML tags, extract text content only
                            if (projectDescription.includes('<') && projectDescription.includes('>')) {
                                // Create a temporary div to extract text content from HTML
                                const tempDiv = document.createElement('div');
                                tempDiv.innerHTML = projectDescription;
                                const textContent = tempDiv.textContent || tempDiv.innerText || '';
                                modalDescription.textContent = textContent.trim() || 'No description provided';
                            } else {
                                modalDescription.textContent = projectDescription;
                            }
                        } else {
                            modalDescription.textContent = 'No description provided';
                        }
                    }

                    // Project image is no longer displayed in modal since Project Content is now on the right side

                    // Update skills
                    if (modalSkillsContainer && projectSkills) {
                        modalSkillsContainer.innerHTML = '';
                        const skills = projectSkills.split(',');
                        skills.forEach(skill => {
                            const skillTag = document.createElement('span');
                            skillTag.className = 'skill-tag';
                            skillTag.style.cssText = 'background: #e8f4fd; color: #0066cc; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.85rem; font-weight: 500;';
                            skillTag.textContent = skill.trim();
                            modalSkillsContainer.appendChild(skillTag);
                        });
                    } else if (modalSkillsContainer) {
                        modalSkillsContainer.innerHTML = '<span style="color: #666; font-style: italic;">No skills specified</span>';
                    }

                    // Update project content
                    if (projectContent && projectContent.trim()) {
                        if (contentSection) contentSection.style.display = 'block';
                        if (projectContentDiv) {
                            projectContentDiv.innerHTML = projectContent;

                            // Clean up any unwanted text content after inserting HTML
                            setTimeout(() => {
                                // Remove all text nodes that contain HTML entities or unwanted characters
                                const walker = document.createTreeWalker(
                                    projectContentDiv,
                                    NodeFilter.SHOW_TEXT,
                                    null,
                                    false
                                );

                                const textNodes = [];
                                let node;
                                while (node = walker.nextNode()) {
                                    textNodes.push(node);
                                }

                                textNodes.forEach(textNode => {
                                    const text = textNode.textContent.trim();
                                    // Remove text nodes that contain HTML entities, special characters, or are just whitespace
                                    if (text.includes('\\x') || text.includes('\\n') || text.includes('b\'') ||
                                        text.match(/^[\s\n\r\t]*$/) || text.match(/[^\w\s]/) || text.length < 3) {
                                        textNode.remove();
                                    }
                                });

                                // Also remove any empty elements
                                const emptyElements = projectContentDiv.querySelectorAll('p:empty, span:empty, div:empty, b:empty, strong:empty');
                                emptyElements.forEach(el => el.remove());
                            }, 100);
                        }
                    } else {
                        if (contentSection) contentSection.style.display = 'none';
                    }

                    // Update related job
                    if (relatedJob && relatedJob.trim()) {
                        if (relatedJobSection) relatedJobSection.style.display = 'block';
                        if (relatedJobText) relatedJobText.textContent = relatedJob;
                    } else {
                        if (relatedJobSection) relatedJobSection.style.display = 'none';
                    }

                    // Open modal
                    portfolioViewModal.classList.add('active');
                    document.body.style.overflow = 'hidden';

                    console.log('Portfolio card clicked:', projectTitle);
                });
            });

            // Fetch and display the hourly_rate from the new API route in the Hourly Rate section
            fetch('/api/genius/hourly_rate')
                .then(response => response.json())
                .then(data => {
                    const el = document.getElementById('hourlyRate');
                    if (data.success && data.hourly_rate !== undefined && data.hourly_rate !== null && data.hourly_rate !== '' && data.hourly_rate != 0) {
                        el.textContent = `$${data.hourly_rate}`;
                    } else {
                        el.textContent = 'No rate set';
                    }
                })
                .catch(() => {
                    document.getElementById('hourlyRate').textContent = 'Unavailable';
                });

        // Portfolio Content Functions
        function addImageUpload() {
            console.log('Image upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.multiple = true; // Allow multiple file selection
            input.style.display = 'none';

            input.onchange = function(e) {
                const files = e.target.files;
                console.log('Files selected:', files.length, 'images');

                if (files.length > 0) {
                    // Process each selected file
                    Array.from(files).forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            console.log(`Image ${index + 1} loaded, adding content block`);
                            addContentBlock('image', {
                                src: e.target.result,
                                name: file.name,
                                size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                            });
                        };
                        reader.readAsDataURL(file);
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addVideoUpload() {
            console.log('Video upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'video/*';
            input.multiple = true; // Allow multiple video selection
            input.style.display = 'none';

            input.onchange = function(e) {
                const files = e.target.files;
                console.log('Video files selected:', files.length, 'videos');

                if (files.length > 0) {
                    Array.from(files).forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            console.log(`Video ${index + 1} loaded, adding content block`);
                            addContentBlock('video', {
                                src: e.target.result,
                                name: file.name,
                                size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                            });
                        };
                        reader.readAsDataURL(file);
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addTextBlock() {
            console.log('Text block button clicked');
            // Create text block modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 10000; display: flex;
                justify-content: center; align-items: center;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 8px; padding: 2rem; max-width: 600px; width: 90%;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        <h3 style="margin: 0; color: #004AAD;">Add Text Block</h3>
                        <div style="display: flex; gap: 1rem;">
                            <button id="plainTextBtn" style="padding: 0.5rem 1rem; border: 1px solid #004AAD; background: #004AAD; color: white; border-radius: 4px; cursor: pointer;">Plain text</button>
                            <button id="markdownBtn" style="padding: 0.5rem 1rem; border: 1px solid #004AAD; background: white; color: #004AAD; border-radius: 4px; cursor: pointer;">Markdown</button>
                        </div>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; font-weight: 600; margin-bottom: 0.5rem;">Heading</label>
                        <input type="text" id="textHeading" placeholder="Enter heading" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <textarea id="textContent" placeholder="Enter your text" style="width: 100%; height: 200px; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"></textarea>
                    </div>
                    <div style="display: flex; justify-content: flex-end; gap: 1rem;">
                        <button id="cancelTextBtn" style="padding: 0.75rem 1.5rem; border: 1px solid #ddd; background: white; color: #666; border-radius: 4px; cursor: pointer;">Cancel</button>
                        <button id="addTextBtn" style="padding: 0.75rem 1.5rem; border: none; background: #CD208B; color: white; border-radius: 4px; cursor: pointer;">Add Text</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            console.log('Text block modal created and added to DOM');

            // Event listeners
            modal.querySelector('#cancelTextBtn').onclick = () => {
                console.log('Text block cancelled');
                document.body.removeChild(modal);
            };
            modal.querySelector('#addTextBtn').onclick = () => {
                const heading = modal.querySelector('#textHeading').value;
                const content = modal.querySelector('#textContent').value;
                console.log('Adding text block:', { heading, content });
                if (content.trim()) {
                    addContentBlock('text', { heading, content });
                    document.body.removeChild(modal);
                } else {
                    alert('Please enter some text content');
                }
            };
        }

        function addPdfUpload() {
            console.log('PDF upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.pdf';
            input.style.display = 'none';

            input.onchange = function(e) {
                console.log('PDF file selected:', e.target.files[0]);
                const file = e.target.files[0];
                if (file) {
                    console.log('Adding PDF content block');
                    addContentBlock('pdf', {
                        name: file.name,
                        size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addMusic() {
            console.log('Music upload button clicked');
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'audio/*';
            input.multiple = true; // Allow multiple audio selection
            input.style.display = 'none';

            input.onchange = function(e) {
                const files = e.target.files;
                console.log('Music files selected:', files.length, 'music files');

                if (files.length > 0) {
                    Array.from(files).forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            console.log(`Music ${index + 1} loaded, adding content block`);
                            addContentBlock('music', {
                                src: e.target.result,
                                name: file.name,
                                size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
                            });
                        };
                        reader.readAsDataURL(file);
                    });
                }
            };

            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        }

        function addLink() {
            console.log('Add link button clicked');
            // Create link modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 10000; display: flex;
                justify-content: center; align-items: center;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 8px; padding: 2rem; max-width: 500px; width: 90%;">
                    <div style="margin-bottom: 1rem;">
                        <h3 style="margin: 0; color: #004AAD;">Add Link</h3>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; font-weight: 600; margin-bottom: 0.5rem;">Link Title</label>
                        <input type="text" id="linkTitle" placeholder="Enter link title" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; font-weight: 600; margin-bottom: 0.5rem;">URL</label>
                        <input type="url" id="linkUrl" placeholder="https://example.com" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div style="display: flex; justify-content: flex-end; gap: 1rem;">
                        <button id="cancelLinkBtn" style="padding: 0.75rem 1.5rem; border: 1px solid #ddd; background: white; color: #666; border-radius: 4px; cursor: pointer;">Cancel</button>
                        <button id="addLinkBtn" style="padding: 0.75rem 1.5rem; border: none; background: #CD208B; color: white; border-radius: 4px; cursor: pointer;">Add Link</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            console.log('Link modal created and added to DOM');

            // Event listeners
            modal.querySelector('#cancelLinkBtn').onclick = () => {
                console.log('Link cancelled');
                document.body.removeChild(modal);
            };
            modal.querySelector('#addLinkBtn').onclick = () => {
                const title = modal.querySelector('#linkTitle').value;
                const url = modal.querySelector('#linkUrl').value;
                console.log('Adding link:', { title, url });
                if (title.trim() && url.trim()) {
                    addContentBlock('link', { title, url });
                    document.body.removeChild(modal);
                } else {
                    alert('Please enter both title and URL');
                }
            };
        }

        function addContentBlock(type, data) {
            const container = document.getElementById('portfolioProjectContent');
            const blockId = 'block_' + Date.now();

            let blockHTML = '';

            switch(type) {
                case 'image':
                    blockHTML = `
                        <div class="content-block" id="${blockId}" style="margin-bottom: 1rem; padding: 0; border: none; border-radius: 4px; position: relative;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px; z-index: 10;">×</button>
                            <img src="${data.src}" alt="" style="width: 100%; height: auto; border-radius: 4px; cursor: pointer; display: block;" onclick="openImagePreview('${data.src}', '')">
                        </div>
                    `;
                    break;
                case 'video':
                    blockHTML = `
                        <div class="content-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <video controls style="max-width: 100%; height: auto; border-radius: 4px;">
                                <source src="${data.src}" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    `;
                    break;
                case 'text':
                    blockHTML = `
                        <div class="content-block text-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative; display: none !important;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            ${data.heading ? `<h4 style="margin: 0 0 0.5rem 0; color: #004AAD;">${data.heading}</h4>` : ''}
                            <p style="margin: 0; line-height: 1.6; white-space: pre-wrap;">${data.content}</p>
                        </div>
                    `;
                    break;
                case 'pdf':
                    blockHTML = `
                        <div class="content-block pdf-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative; display: none !important; align-items: center; gap: 1rem;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <i class="fas fa-file-pdf" style="font-size: 2rem; color: #ff4444;"></i>
                            <div>
                                <p style="margin: 0; font-weight: 600;">${data.name}</p>
                                <p style="margin: 0; font-size: 0.9rem; color: #666;">${data.size}</p>
                            </div>
                        </div>
                    `;
                    break;
                case 'music':
                    blockHTML = `
                        <div class="content-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <audio controls style="width: 100%;">
                                <source src="${data.src}" type="audio/mpeg">
                                Your browser does not support the audio element.
                            </audio>
                        </div>
                    `;
                    break;
                case 'link':
                    blockHTML = `
                        <div class="content-block link-block" id="${blockId}" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; position: relative; display: none !important; align-items: center; gap: 1rem;">
                            <button onclick="removeBlock('${blockId}')" style="position: absolute; top: 5px; right: 5px; background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; font-size: 12px;">×</button>
                            <i class="fas fa-link" style="font-size: 1.5rem; color: #004AAD;"></i>
                            <div style="flex: 1;">
                                <a href="${data.url}" target="_blank" style="color: #004AAD; text-decoration: none; font-weight: 600; display: block;">${data.title}</a>
                                <p style="margin: 0; font-size: 0.9rem; color: #666; word-break: break-all;">${data.url}</p>
                            </div>
                        </div>
                    `;
                    break;
            }

            container.insertAdjacentHTML('beforeend', blockHTML);
        }

        function removeBlock(blockId) {
            const block = document.getElementById(blockId);
            if (block) {
                block.remove();
            }
        }

        function openImagePreview(src, name) {
            console.log('Opening image preview for:', name);
            // Create image preview modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 10000; display: flex;
                justify-content: center; align-items: center; cursor: pointer;
            `;

            modal.innerHTML = `
                <div style="max-width: 90%; max-height: 90%; position: relative;">
                    <button onclick="this.parentElement.parentElement.remove()" style="position: absolute; top: -40px; right: 0; background: white; color: #333; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; font-size: 16px;">×</button>
                    <img src="${src}" alt="" style="max-width: 100%; max-height: 100%; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
                </div>
            `;

            // Close modal when clicking outside the image
            modal.onclick = function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            };

            document.body.appendChild(modal);
        }

        // Setup portfolio button event listeners
        function setupPortfolioButtons() {
            console.log('Setting up portfolio button listeners');
            const imageBtn = document.getElementById('uploadImageBtn');
            const videoBtn = document.getElementById('portfolioUploadVideoBtn');
            const textBtn = document.getElementById('addTextBlockBtn');
            const linkBtn = document.getElementById('addLinkBtn');
            const pdfBtn = document.getElementById('uploadPdfBtn');
            const musicBtn = document.getElementById('addMusicBtn');

            console.log('Image button found:', imageBtn);
            console.log('Video button found:', videoBtn);
            console.log('Text button found:', textBtn);
            console.log('Link button found:', linkBtn);
            console.log('PDF button found:', pdfBtn);
            console.log('Music button found:', musicBtn);

            if (imageBtn) {
                imageBtn.removeEventListener('click', addImageUpload); // Remove existing listener
                imageBtn.addEventListener('click', addImageUpload);
            }
            if (videoBtn) {
                videoBtn.removeEventListener('click', addVideoUpload);
                videoBtn.addEventListener('click', addVideoUpload);
            }
            if (textBtn) {
                textBtn.removeEventListener('click', addTextBlock);
                textBtn.addEventListener('click', addTextBlock);
            }
            if (linkBtn) {
                linkBtn.removeEventListener('click', addLink);
                linkBtn.addEventListener('click', addLink);
            }
            if (pdfBtn) {
                pdfBtn.removeEventListener('click', addPdfUpload);
                pdfBtn.addEventListener('click', addPdfUpload);
            }
            if (musicBtn) {
                musicBtn.removeEventListener('click', addMusic);
                musicBtn.addEventListener('click', addMusic);
            }
        }

        });

        // Header functionality
        document.addEventListener('DOMContentLoaded', function() {

            // Profile dropdown functionality
            const profileButton = document.querySelector('.profile-button');
            const profileDropdown = document.querySelector('.profile-dropdown');
            const profileDropdownContent = document.querySelector('.profile-dropdown-content');

            if (profileButton && profileDropdown && profileDropdownContent) {
                profileButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    profileDropdownContent.classList.toggle('active');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!profileDropdown.contains(e.target)) {
                        profileDropdownContent.classList.remove('active');
                    }
                });

                // Close dropdown when pressing Escape
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape') {
                        profileDropdownContent.classList.remove('active');
                        profileButton.focus();
                    }
                });

                // Keyboard navigation within dropdown
                profileDropdownContent.addEventListener('keydown', function(e) {
                    const links = Array.from(profileDropdownContent.querySelectorAll('a'));
                    const currentIndex = links.indexOf(document.activeElement);

                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        const nextIndex = (currentIndex + 1) % links.length;
                        links[nextIndex].focus();
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        const prevIndex = (currentIndex - 1 + links.length) % links.length;
                        links[prevIndex].focus();
                    } else if (e.key === 'Escape') {
                        e.preventDefault();
                        profileDropdownContent.classList.remove('active');
                        profileButton.focus();
                    }
                });
            }

            // Notification functionality
            const notificationBell = document.getElementById('notification-bell');
            const notificationDropdown = document.querySelector('.notification-dropdown');

            if (notificationBell && notificationDropdown) {
                notificationBell.addEventListener('click', function(e) {
                    e.stopPropagation();
                    notificationDropdown.classList.toggle('active');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!notificationDropdown.contains(e.target) && !notificationBell.contains(e.target)) {
                        notificationDropdown.classList.remove('active');
                    }
                });
            }

            // Search functionality
            function performSearch() {
                const searchInput = document.getElementById('searchInput');
                if (searchInput && searchInput.value.trim()) {
                    console.log('Searching for:', searchInput.value);
                    // Add search functionality here
                }
            }

            // Make performSearch available globally
            window.performSearch = performSearch;
        });
    </script>
</body>
</html>



